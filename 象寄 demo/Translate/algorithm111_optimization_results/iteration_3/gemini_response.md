当然，作为一名计算机视觉和布局分析算法专家，我将对您提供的数据和算法进行深入分析，并提出一套完整的优化方案。

---

## 图片布局分析结果

以下是我对每张图片布局的专业人工分析，这将作为我们优化的“基准真相”(Ground Truth)。

*   **1.jpg**: 包含两个主要分组。
    *   **组1 (顶部)**: `['4D高回弹记忆棉', '久睡不塌 适用更久']`。这是一个整体，**居中对齐**。
    *   **组2 (底部)**: `['“0压感”', '高回弹海绵', '深度分散压力', '不易塌陷']`。这是一个整体，**左对齐**。
    *   **主布局**: `multi_style_complex`，主对齐应为**居中**（基于顶部标题的视觉重要性）。

*   **2.jpg**: 这是一个典型的**表格布局**。
    *   **组1 (页脚)**: `['*产品尺寸...']`，居中对齐的单个元素。
    *   **组2 (主内容)**: 一个2x4的表格。所有单元格内容均为**左对齐**。
    *   **主布局**: `table`，主对齐为**左对齐**。

*   **3.jpg**: 这是一个单一的文本块，但内部对齐方式复杂。
    *   **组1 (整体)**: 标题`'店铺重要通知'`是**居中对齐**。下面的三行正文是**左对齐**。最后的按钮`'确定收到'`是**居中对齐**。
    *   **主布局**: `multi_style_simple` (因为是单个视觉块)，主对齐为**左对齐** (基于正文内容量)。

*   **4.png**: 这是一个多栏对比布局。
    *   **组1 (右上)**: `['加粗钢管', '约15mm']`，**居中对齐**。
    *   **组2 (左下)**: `['加粗升级款', '约15*15mm...', '稳稳承托...']`，**左对齐**。
    *   **组3 (右下)**: `['常规薄款', '约10*10mm...', '框架薄...']`，**左对齐**。
    *   **主布局**: `multi_column` 或 `distributed`，主对齐为**左对齐**。

*   **5.jpg**: 这是一个网格(Grid)布局。
    *   **组1 (标题)**: `['花样收纳', '想吸哪里吸哪里']`，**居中对齐**。
    *   **组2,3,4,5 (网格项)**: 四个独立的标签 `['电视遥控']`, `['马桶遥控']`, `['窗帘遥控']`, `['空调遥控']`，每个都是独立的**居中对齐**小组。
    *   **主布局**: `grid` 或 `distributed`，主对齐为**居中**。

*   **6.jpg**: 布局非常复杂，是典型的自由式/海报式布局。
    *   多个独立的小组，对齐方式各不相同（左上角品牌logo区、中间大标题居中、下方两个圆形徽章、底部横幅文字居中）。
    *   **主布局**: `distributed` 或 `free_style`，主对齐为**居中**（基于大标题）。

*   **7.jpg**: 包含三个由大间距分开的段落。
    *   **组1**: `['源自核心产区茶园', '每一颗...']`，**居中对齐**。
    *   **组2**: `['选自高山野长茶树']`，**居中对齐**。
    *   **组3**: `['成长于白茶黄金生长区...']`，段落内容为**左对齐**。
    *   **主布局**: `multi_style_complex`，主对齐为**居中**。

*   **8.jpg**: 典型的标注/散点式布局。
    *   三个独立的标签 `['洗脸盆']`, `['洗衣盆']`, `['婴儿盆']`，它们的位置由指向的物体决定，没有共同的对齐基线。
    *   **主布局**: `distributed`，主对齐为**mixed**或`none`。

*   **9.png**: 这是一个垂直分栏的图文介绍。
    *   **组1 (标题)**: `['我们用心...']`，**居中对齐**。
    *   **组2 (小口袋)**: `['小口袋', '可放置...']`，**左对齐**。
    *   **组3 (大口袋)**: `['大口袋', '可以放...']`，**左对齐**。
    *   **主布局**: `multi_style_complex`，主对齐为**左对齐**。

*   **10.png**: 一个非常清晰的单一文本块。
    *   **组1 (整体)**: 所有三行文字 `['可背也可提', '轻量便捷', '轻轻松松...']` 构成一个整体，**居中对齐**。
    *   **主布局**: `multi_style_simple`，主对齐为**居中**。

## 当前算法问题诊断

V3算法虽然引入了行聚合和块构建，但存在几个核心问题导致了误判：

1.  **分组逻辑过于简单 (`_build_blocks`)**:
    *   **问题**: 算法仅基于“垂直距离”和“水平重叠”来合并行。这导致了两个主要错误：
        *   **错误合并**: 在表格布局（图2）和多栏布局（图4）中，左右两栏因为垂直上靠近且水平范围有重叠，被错误地合并成一个大块，从而无法识别出`table`或`multi-column`结构。
        *   **错误拆分**: 在一些视觉上连续的块中（如图1底部），如果某两行之间恰好没有水平重叠（例如，一行短一行长），它们可能会被错误地拆分成两个块。
    *   **证据**: 图2被识别为`multi_style_complex`而非`table`；图1的分组情况过于零碎。

2.  **对齐检测不够鲁棒 (`_get_alignment`)**:
    *   **问题**: 使用`median`和`group_width`作为容差基准，在某些情况下不稳定。
        *   当一个组内文本框数量少时（如<3），中位数可能没有代表性。
        *   当一个组内某行文本特别长时，`group_width`会变得很大，导致容差`tolerance`过大，从而将不相关的对齐也误判为对齐。
    *   **证据**: 图10被错误识别为`right`对齐，而它显然是`center`对齐。这表明对齐检测逻辑存在严重缺陷。图3也被误判为`right`。

3.  **布局模式定义和检测不完善**:
    *   **问题**: `table`和`distributed`的检测逻辑依赖于前面分组步骤的正确性，一旦分组出错，后续检测必然失败。
        *   `_detect_table_layout`仅检查左对齐的组，过于局限。
        *   `_detect_distributed_layout`基于“小分组”的数量，这是一个间接且不可靠的特征。真正的分布式布局是关于空间的分散性。
    *   **证据**: 图2（表格）、图5（网格）、图8（散点）均未被识别出其真实的结构，而是被归类为通用的`multi_style`。

## 优化策略

为了解决上述问题，我将设计`LayoutAnalyzerV4`，其核心思想是从**基于规则的线性扫描**转向**基于图的结构化聚类**。

1.  **核心改进：基于图的块聚类 (`Graph-based Clustering`)**
    *   **思路**: 将每个文本框视为图中的一个节点。如果两个文本框在视觉上“相关”，则在它们之间连接一条边。最后，图中的每个连通分量就构成一个视觉块。
    *   **“相关”的定义**: 两个框`A`和`B`相关，如果满足以下任一条件：
        *   **强垂直关系**: `A`和`B`水平重叠度高，且垂直间距小（例如小于平均行高）。这是构成段落的主要条件。
        *   **强水平关系**: `A`和`B`垂直中心对齐度高，且水平间距小。这用于连接同一行内的多个文本框。
    *   **优势**: 这种方法不再受制于文本框的排序，能自然地处理多栏、表格等复杂布局，正确地将不相交但视觉相关的元素聚合，同时分离不相关的元素。

2.  **对齐检测精度提升**
    *   **思路**: 放弃基于中位数和组宽的容差，改用更稳健的统计量——**标准差 (Standard Deviation)**。
    *   **实现**: 对于一个块内的所有文本框，分别计算其左边界(`x1`)、中心点(`cx`)、右边界(`x2`)坐标的标准差。标准差最小的那一组坐标，就代表了这个块的主要对齐方式。
    *   **鲁棒性**: 标准差对所有点都敏感，能更好地反映整体的离散程度。我们可以设置一个阈值（例如，小于平均字符宽度），如果最小标准差仍大于此阈值，则判定为`mixed`对齐。

3.  **高级布局模式重定义与检测**
    *   **前提**: 在完成了可靠的图聚类之后，我们得到了一系列高质量的`TextBlock`对象。
    *   **`table`检测**:
        *   寻找多个在**垂直方向上对齐**的块（它们的中心X坐标相近），形成“列”。
        *   如果能找到至少2个“列”，且每列包含至少2个块，则判定为`table`布局。
    *   **`distributed` / `grid` 检测**:
        *   如果聚类后产生大量（如≥3）的小块（每块文本框≤3）。
        *   并且这些块的凸包（Convex Hull）面积远大于块自身面积之和，说明它们在空间上是分散的。
    *   **`multi_column` 检测**:
        *   如果存在多个块，它们在垂直方向上不重叠，但在水平方向上并排排列。

4.  **引入`TextBlock`类**
    *   为了代码的清晰和模块化，创建一个`TextBlock`类来封装一个聚类后的块。该类将包含块内的所有`TextBox`对象，并能计算自身的边界框、对齐方式、总面积等属性。

## 优化后的算法实现

```python
import numpy as np
from collections import defaultdict

class TextBox:
    """
    文本框类，与V3基本一致，但增加了ID以便于图操作。
    """
    def __init__(self, bbox, text=""):
        self.x1, self.y1, self.x2, self.y2 = bbox
        self.text = text
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.cx = self.x1 + self.w / 2
        self.cy = self.y1 + self.h / 2
        self.area = self.w * self.h
        self.font_size = self.h 
        self.id = id(self)

    def __repr__(self):
        return f"Box(text='{self.text}', bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"

    def horizontal_overlap(self, other):
        """计算与另一个框的水平重叠度"""
        return max(0, min(self.x2, other.x2) - max(self.x1, other.x1))

class TextBlock:
    """
    表示一个由多个TextBox组成的视觉块。
    """
    def __init__(self, boxes):
        self.boxes = sorted(boxes, key=lambda b: b.y1)
        self.num_boxes = len(self.boxes)
        self.x1 = min(b.x1 for b in self.boxes)
        self.y1 = min(b.y1 for b in self.boxes)
        self.x2 = max(b.x2 for b in self.boxes)
        self.y2 = max(b.y2 for b in self.boxes)
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.total_area = sum(b.area for b in self.boxes)
        self.alignment = 'unknown' # 将在分析阶段确定

    def __repr__(self):
        return f"TextBlock(boxes={self.num_boxes}, alignment='{self.alignment}', bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"


class LayoutAnalyzerV4:
    """
    布局分析算法 V4
    - 核心改进:
      1. 引入图聚类算法进行块构建，取代脆弱的行聚合。
      2. 基于标准差的对齐检测，更鲁棒、更精确。
      3. 基于高质量块的高级布局模式（表格、分布式）检测。
      4. 引入TextBlock类，代码结构更清晰。
    """
    def __init__(self, text_boxes,
                 v_dist_ratio=1.5,      # 垂直分组距离阈值（倍于行高）
                 h_dist_ratio=2.0,      # 水平分组距离阈值（倍于行高）
                 h_overlap_ratio=0.1,   # 垂直分组的最小水平重叠率
                 align_std_thresh_ratio=0.5): # 对齐标准差阈值（倍于平均字符高度）
        self.boxes = [TextBox(b, t) for b, t in text_boxes]
        self.V_DIST_RATIO = v_dist_ratio
        self.H_DIST_RATIO = h_dist_ratio
        self.H_OVERLAP_RATIO = h_overlap_ratio
        self.ALIGN_STD_THRESH_RATIO = align_std_thresh_ratio

    def _build_graph(self):
        """构建文本框之间的关系图"""
        adj = defaultdict(list)
        if len(self.boxes) < 2:
            return adj

        for i in range(len(self.boxes)):
            for j in range(i + 1, len(self.boxes)):
                b1, b2 = self.boxes[i], self.boxes[j]
                avg_h = (b1.h + b2.h) / 2

                # 条件1: 强垂直关系 (用于段落聚合)
                v_dist = max(0, max(b1.y1, b2.y1) - min(b1.y2, b2.y2))
                h_overlap = b1.horizontal_overlap(b2)
                if v_dist < self.V_DIST_RATIO * avg_h and h_overlap > self.H_OVERLAP_RATIO * min(b1.w, b2.w):
                    adj[i].append(j)
                    adj[j].append(i)
                    continue

                # 条件2: 强水平关系 (用于行内聚合)
                h_dist = max(0, max(b1.x1, b2.x1) - min(b1.x2, b2.x2))
                v_overlap = max(0, min(b1.y2, b2.y2) - max(b1.y1, b2.y1))
                if h_dist < self.H_DIST_RATIO * avg_h and v_overlap > 0.5 * min(b1.h, b2.h):
                    adj[i].append(j)
                    adj[j].append(i)
        return adj

    def _find_blocks_with_graph(self):
        """使用图的连通分量来寻找文本块"""
        if not self.boxes:
            return []
        
        adj = self._build_graph()
        visited = set()
        blocks = []
        for i in range(len(self.boxes)):
            if i not in visited:
                component_indices = []
                q = [i]
                visited.add(i)
                while q:
                    u = q.pop(0)
                    component_indices.append(u)
                    for v in adj[u]:
                        if v not in visited:
                            visited.add(v)
                            q.append(v)
                
                component_boxes = [self.boxes[k] for k in component_indices]
                blocks.append(TextBlock(component_boxes))
        
        return sorted(blocks, key=lambda blk: blk.y1)

    def _get_block_alignment(self, block):
        """V4对齐检测：基于标准差，更稳健"""
        if block.num_boxes < 2:
            return 'center' # 单行或单个框默认为居中

        boxes = block.boxes
        lefts = [b.x1 for b in boxes]
        centers = [b.cx for b in boxes]
        rights = [b.x2 for b in boxes]

        # 计算标准差
        std_devs = {
            'left': np.std(lefts),
            'center': np.std(centers),
            'right': np.std(rights)
        }

        best_align = min(std_devs, key=std_devs.get)
        
        # 检查最佳对齐的质量
        avg_char_height = np.mean([b.h for b in boxes])
        # 如果最小的标准差仍然很大，说明对齐很差，判定为混合
        if std_devs[best_align] > self.ALIGN_STD_THRESH_RATIO * avg_char_height:
            return 'mixed'
            
        return best_align

    def _detect_table_layout(self, blocks):
        """检测表格布局"""
        if len(blocks) < 4: return False # 至少需要2x2=4个单元格
        
        # 按水平位置对块进行聚类，形成列
        cols = defaultdict(list)
        sorted_blocks = sorted(blocks, key=lambda b: b.x1)
        
        for block in sorted_blocks:
            found_col = False
            # 尝试将块放入现有列
            for col_key in cols:
                # 如果块的中心与列的平均中心接近
                if abs(block.x1 - col_key) < block.w:
                    cols[col_key].append(block)
                    found_col = True
                    break
            if not found_col:
                cols[block.x1].append(block)
        
        # 过滤掉只有一个元素的列
        valid_cols = [c for c in cols.values() if len(c) > 1]
        
        # 如果有多于一列，且列数*行数接近总块数，则为表格
        if len(valid_cols) >= 2:
            return True
        return False

    def _detect_distributed_layout(self, blocks):
        """检测分布式/网格/散点布局"""
        if len(blocks) < 3: return False
        
        # 计算块的总面积与它们凸包面积的比率
        total_block_area = sum(b.w * b.h for b in blocks)
        
        # 计算凸包
        overall_x1 = min(b.x1 for b in blocks)
        overall_y1 = min(b.y1 for b in blocks)
        overall_x2 = max(b.x2 for b in blocks)
        overall_y2 = max(b.y2 for b in blocks)
        convex_hull_area = (overall_x2 - overall_x1) * (overall_y2 - overall_y1)

        # 如果块面积之和只占凸包面积的一小部分，说明它们很分散
        if convex_hull_area > 0 and total_block_area / convex_hull_area < 0.4:
            return True
        return False

    def analyze(self):
        """执行完整的V4布局分析"""
        if not self.boxes:
            return {"layout_mode": "no_text", "main_alignment": "none", "blocks": []}

        blocks = self._find_blocks_with_graph()
        
        for block in blocks:
            block.alignment = self._get_block_alignment(block)

        # --- 最终布局决策 ---
        layout_mode = "unknown"
        if self._detect_table_layout(blocks):
            layout_mode = "table"
        elif self._detect_distributed_layout(blocks):
            layout_mode = "distributed"
        else:
            if len(blocks) <= 1:
                layout_mode = "single_block"
            else:
                layout_mode = "multi_block"

        # 判断主对齐方式（加权投票）
        alignment_scores = defaultdict(float)
        for block in blocks:
            # 权重 = 文本框数量 * sqrt(总面积) -> 降低超大块的极端影响
            weight = block.num_boxes * np.sqrt(block.total_area)
            alignment_scores[block.alignment] += weight
        
        main_alignment = "none"
        if alignment_scores:
            non_mixed_scores = {k: v for k, v in alignment_scores.items() if k != 'mixed'}
            if non_mixed_scores:
                main_alignment = max(non_mixed_scores, key=non_mixed_scores.get)
            else:
                main_alignment = max(alignment_scores, key=alignment_scores.get)

        return {
            "layout_mode": layout_mode,
            "main_alignment": main_alignment,
            "blocks": blocks
        }

# --- 示例使用 ---
if __name__ == '__main__':
    # 模拟图片2 (2.jpg) 的BBOX数据，这是一个表格布局
    # (bbox, text)
    boxes_data_img2 = [
        ([100, 200, 200, 220], '产品名称'), ([500, 200, 550, 220], '尺寸'),
        ([100, 230, 350, 250], '吸盘兔型湿厕巾收纳架'), ([500, 230, 700, 250], '卷纸款：...'),
        ([100, 300, 200, 320], '产品材质'), ([500, 300, 550, 320], '颜色'),
        ([100, 330, 280, 350], '不锈钢+ABS'), ([500, 330, 620, 350], '胡桃木色'),
        ([300, 100, 700, 120], '*产品尺寸...'),
    ]

    print("--- V4 分析图片2 (表格) ---")
    analyzer2 = LayoutAnalyzerV4(boxes_data_img2)
    result2 = analyzer2.analyze()
    print(f"布局模式: {result2['layout_mode']}")
    print(f"主对齐: {result2['main_alignment']}")
    print(f"分组数量: {len(result2['blocks'])}")
    for i, block in enumerate(result2['blocks']):
        print(f"  Block {i+1}: {block}")

    print("\n" + "="*30 + "\n")

    # 模拟图片10 (10.png) 的BBOX数据，这是一个居中布局
    boxes_data_img10 = [
        ([300, 200, 700, 250], '可背也可提'),
        ([350, 260, 650, 300], '轻量便捷'),
        ([250, 350, 750, 390], '轻轻松松一提就走'),
    ]
    
    print("--- V4 分析图片10 (居中) ---")
    analyzer10 = LayoutAnalyzerV4(boxes_data_img10)
    result10 = analyzer10.analyze()
    print(f"布局模式: {result10['layout_mode']}")
    print(f"主对齐: {result10['main_alignment']}")
    print(f"分组数量: {len(result10['blocks'])}")
    for i, block in enumerate(result10['blocks']):
        print(f"  Block {i+1}: {block}")

```

## 预期改进效果

1.  **分组准确性大幅提升**:
    *   **表格/多栏 (图2, 4)**: 图聚类算法将能正确地将垂直对齐但水平分离的列识别为独立的块，为`table`检测奠定基础。
    *   **视觉连贯性 (图1, 7)**: 算法不再会因为微小的对齐变化或无水平重叠而错误地拆分视觉上连续的块。

2.  **对齐识别准确率提升**:
    *   **居中/右对齐修正 (图10, 3)**: 基于标准差的对齐检测对离群点和少量数据点更鲁棒，能正确识别出图10的`center`对齐和图3中正文的`left`对齐。

3.  **高级布局模式识别能力**:
    *   **表格 (图2)**: 将被正确识别为`table`。
    *   **分布式/网格 (图5, 8)**: 基于空间分散度的检测方法，将能准确识别出`distributed`布局，而不是模糊地归为`multi_style_complex`。

4.  **整体鲁棒性**:
    *   新算法的逻辑更接近人类视觉感知系统（先聚类，再分析每个聚类的属性，最后看整体关系），减少了对特定阈值和顺序的依赖，对各种复杂和不规则的布局有更强的适应能力。预期在处理多样化的电商详情页图片时，准确率和稳定性都会有显著提高。