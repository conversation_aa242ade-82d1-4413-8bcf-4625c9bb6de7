"""
第3次迭代优化的布局识别算法
生成时间: 2025-07-14T23:18:54.409067
"""

import numpy as np
from collections import defaultdict

class TextBox:
    """
    文本框类，与V3基本一致，但增加了ID以便于图操作。
    """
    def __init__(self, bbox, text=""):
        self.x1, self.y1, self.x2, self.y2 = bbox
        self.text = text
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.cx = self.x1 + self.w / 2
        self.cy = self.y1 + self.h / 2
        self.area = self.w * self.h
        self.font_size = self.h 
        self.id = id(self)

    def __repr__(self):
        return f"Box(text='{self.text}', bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"

    def horizontal_overlap(self, other):
        """计算与另一个框的水平重叠度"""
        return max(0, min(self.x2, other.x2) - max(self.x1, other.x1))

class TextBlock:
    """
    表示一个由多个TextBox组成的视觉块。
    """
    def __init__(self, boxes):
        self.boxes = sorted(boxes, key=lambda b: b.y1)
        self.num_boxes = len(self.boxes)
        self.x1 = min(b.x1 for b in self.boxes)
        self.y1 = min(b.y1 for b in self.boxes)
        self.x2 = max(b.x2 for b in self.boxes)
        self.y2 = max(b.y2 for b in self.boxes)
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.total_area = sum(b.area for b in self.boxes)
        self.alignment = 'unknown' # 将在分析阶段确定

    def __repr__(self):
        return f"TextBlock(boxes={self.num_boxes}, alignment='{self.alignment}', bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"


class LayoutAnalyzerV4:
    """
    布局分析算法 V4
    - 核心改进:
      1. 引入图聚类算法进行块构建，取代脆弱的行聚合。
      2. 基于标准差的对齐检测，更鲁棒、更精确。
      3. 基于高质量块的高级布局模式（表格、分布式）检测。
      4. 引入TextBlock类，代码结构更清晰。
    """
    def __init__(self, text_boxes,
                 v_dist_ratio=1.5,      # 垂直分组距离阈值（倍于行高）
                 h_dist_ratio=2.0,      # 水平分组距离阈值（倍于行高）
                 h_overlap_ratio=0.1,   # 垂直分组的最小水平重叠率
                 align_std_thresh_ratio=0.5): # 对齐标准差阈值（倍于平均字符高度）
        self.boxes = [TextBox(b, t) for b, t in text_boxes]
        self.V_DIST_RATIO = v_dist_ratio
        self.H_DIST_RATIO = h_dist_ratio
        self.H_OVERLAP_RATIO = h_overlap_ratio
        self.ALIGN_STD_THRESH_RATIO = align_std_thresh_ratio

    def _build_graph(self):
        """构建文本框之间的关系图"""
        adj = defaultdict(list)
        if len(self.boxes) < 2:
            return adj

        for i in range(len(self.boxes)):
            for j in range(i + 1, len(self.boxes)):
                b1, b2 = self.boxes[i], self.boxes[j]
                avg_h = (b1.h + b2.h) / 2

                # 条件1: 强垂直关系 (用于段落聚合)
                v_dist = max(0, max(b1.y1, b2.y1) - min(b1.y2, b2.y2))
                h_overlap = b1.horizontal_overlap(b2)
                if v_dist < self.V_DIST_RATIO * avg_h and h_overlap > self.H_OVERLAP_RATIO * min(b1.w, b2.w):
                    adj[i].append(j)
                    adj[j].append(i)
                    continue

                # 条件2: 强水平关系 (用于行内聚合)
                h_dist = max(0, max(b1.x1, b2.x1) - min(b1.x2, b2.x2))
                v_overlap = max(0, min(b1.y2, b2.y2) - max(b1.y1, b2.y1))
                if h_dist < self.H_DIST_RATIO * avg_h and v_overlap > 0.5 * min(b1.h, b2.h):
                    adj[i].append(j)
                    adj[j].append(i)
        return adj

    def _find_blocks_with_graph(self):
        """使用图的连通分量来寻找文本块"""
        if not self.boxes:
            return []
        
        adj = self._build_graph()
        visited = set()
        blocks = []
        for i in range(len(self.boxes)):
            if i not in visited:
                component_indices = []
                q = [i]
                visited.add(i)
                while q:
                    u = q.pop(0)
                    component_indices.append(u)
                    for v in adj[u]:
                        if v not in visited:
                            visited.add(v)
                            q.append(v)
                
                component_boxes = [self.boxes[k] for k in component_indices]
                blocks.append(TextBlock(component_boxes))
        
        return sorted(blocks, key=lambda blk: blk.y1)

    def _get_block_alignment(self, block):
        """V4对齐检测：基于标准差，更稳健"""
        if block.num_boxes < 2:
            return 'center' # 单行或单个框默认为居中

        boxes = block.boxes
        lefts = [b.x1 for b in boxes]
        centers = [b.cx for b in boxes]
        rights = [b.x2 for b in boxes]

        # 计算标准差
        std_devs = {
            'left': np.std(lefts),
            'center': np.std(centers),
            'right': np.std(rights)
        }

        best_align = min(std_devs, key=std_devs.get)
        
        # 检查最佳对齐的质量
        avg_char_height = np.mean([b.h for b in boxes])
        # 如果最小的标准差仍然很大，说明对齐很差，判定为混合
        if std_devs[best_align] > self.ALIGN_STD_THRESH_RATIO * avg_char_height:
            return 'mixed'
            
        return best_align

    def _detect_table_layout(self, blocks):
        """检测表格布局"""
        if len(blocks) < 4: return False # 至少需要2x2=4个单元格
        
        # 按水平位置对块进行聚类，形成列
        cols = defaultdict(list)
        sorted_blocks = sorted(blocks, key=lambda b: b.x1)
        
        for block in sorted_blocks:
            found_col = False
            # 尝试将块放入现有列
            for col_key in cols:
                # 如果块的中心与列的平均中心接近
                if abs(block.x1 - col_key) < block.w:
                    cols[col_key].append(block)
                    found_col = True
                    break
            if not found_col:
                cols[block.x1].append(block)
        
        # 过滤掉只有一个元素的列
        valid_cols = [c for c in cols.values() if len(c) > 1]
        
        # 如果有多于一列，且列数*行数接近总块数，则为表格
        if len(valid_cols) >= 2:
            return True
        return False

    def _detect_distributed_layout(self, blocks):
        """检测分布式/网格/散点布局"""
        if len(blocks) < 3: return False
        
        # 计算块的总面积与它们凸包面积的比率
        total_block_area = sum(b.w * b.h for b in blocks)
        
        # 计算凸包
        overall_x1 = min(b.x1 for b in blocks)
        overall_y1 = min(b.y1 for b in blocks)
        overall_x2 = max(b.x2 for b in blocks)
        overall_y2 = max(b.y2 for b in blocks)
        convex_hull_area = (overall_x2 - overall_x1) * (overall_y2 - overall_y1)

        # 如果块面积之和只占凸包面积的一小部分，说明它们很分散
        if convex_hull_area > 0 and total_block_area / convex_hull_area < 0.4:
            return True
        return False

    def analyze(self):
        """执行完整的V4布局分析"""
        if not self.boxes:
            return {"layout_mode": "no_text", "main_alignment": "none", "blocks": []}

        blocks = self._find_blocks_with_graph()
        
        for block in blocks:
            block.alignment = self._get_block_alignment(block)

        # --- 最终布局决策 ---
        layout_mode = "unknown"
        if self._detect_table_layout(blocks):
            layout_mode = "table"
        elif self._detect_distributed_layout(blocks):
            layout_mode = "distributed"
        else:
            if len(blocks) <= 1:
                layout_mode = "single_block"
            else:
                layout_mode = "multi_block"

        # 判断主对齐方式（加权投票）
        alignment_scores = defaultdict(float)
        for block in blocks:
            # 权重 = 文本框数量 * sqrt(总面积) -> 降低超大块的极端影响
            weight = block.num_boxes * np.sqrt(block.total_area)
            alignment_scores[block.alignment] += weight
        
        main_alignment = "none"
        if alignment_scores:
            non_mixed_scores = {k: v for k, v in alignment_scores.items() if k != 'mixed'}
            if non_mixed_scores:
                main_alignment = max(non_mixed_scores, key=non_mixed_scores.get)
            else:
                main_alignment = max(alignment_scores, key=alignment_scores.get)

        return {
            "layout_mode": layout_mode,
            "main_alignment": main_alignment,
            "blocks": blocks
        }

# --- 示例使用 ---
if __name__ == '__main__':
    # 模拟图片2 (2.jpg) 的BBOX数据，这是一个表格布局
    # (bbox, text)
    boxes_data_img2 = [
        ([100, 200, 200, 220], '产品名称'), ([500, 200, 550, 220], '尺寸'),
        ([100, 230, 350, 250], '吸盘兔型湿厕巾收纳架'), ([500, 230, 700, 250], '卷纸款：...'),
        ([100, 300, 200, 320], '产品材质'), ([500, 300, 550, 320], '颜色'),
        ([100, 330, 280, 350], '不锈钢+ABS'), ([500, 330, 620, 350], '胡桃木色'),
        ([300, 100, 700, 120], '*产品尺寸...'),
    ]

    print("--- V4 分析图片2 (表格) ---")
    analyzer2 = LayoutAnalyzerV4(boxes_data_img2)
    result2 = analyzer2.analyze()
    print(f"布局模式: {result2['layout_mode']}")
    print(f"主对齐: {result2['main_alignment']}")
    print(f"分组数量: {len(result2['blocks'])}")
    for i, block in enumerate(result2['blocks']):
        print(f"  Block {i+1}: {block}")

    print("\n" + "="*30 + "\n")

    # 模拟图片10 (10.png) 的BBOX数据，这是一个居中布局
    boxes_data_img10 = [
        ([300, 200, 700, 250], '可背也可提'),
        ([350, 260, 650, 300], '轻量便捷'),
        ([250, 350, 750, 390], '轻轻松松一提就走'),
    ]
    
    print("--- V4 分析图片10 (居中) ---")
    analyzer10 = LayoutAnalyzerV4(boxes_data_img10)
    result10 = analyzer10.analyze()
    print(f"布局模式: {result10['layout_mode']}")
    print(f"主对齐: {result10['main_alignment']}")
    print(f"分组数量: {len(result10['blocks'])}")
    for i, block in enumerate(result10['blocks']):
        print(f"  Block {i+1}: {block}")
