好的，作为一名专业的计算机视觉和布局分析算法专家，我将对您提供的数据和算法进行深入分析，并提出一套完整的优化方案。

---

## 图片布局分析结果

以下是我对每张图片布局的专业人工分析，这将作为我们评估和优化的“黄金标准” (Ground Truth)。

-   **1.jpg**:
    -   **分组**: 2个主要分组。
        1.  顶部标题块: `['4D高回弹记忆棉', '久睡不塌', '适用更久']`。
        2.  底部说明块: `['“0压感”', '高回弹海绵', '深度分散压力', '不易塌陷']`。
    -   **对齐**: 顶部块为 **居中对齐**。底部块为 **左对齐**。
    -   **主布局**: 整体由顶部的大号居中标题主导，应为 **主居中**。

-   **10.png**:
    -   **分组**: 1个主要分组。所有文字 `['可背也可提', '轻量便捷', '轻轻松松一提就走']` 构成一个视觉整体。
    -   **对齐**: 整体为 **居中对齐**。
    -   **主布局**: **主居中**。

-   **2.jpg**:
    -   **分组**: 这是一个明确的 **表格 (Table)** 布局，可以看作一个2列4行的结构，外加一个顶部的备注行。
        -   列1: `['产品名称', '吸盘兔型...', '产品材质', '不锈钢+ABS']`
        -   列2: `['尺寸', '卷纸款...', '颜色', '胡桃木色']`
    -   **对齐**: 表格内部每列均为 **左对齐**。顶部的备注行为 **居中对齐**。
    -   **主布局**: **表格布局**。

-   **3.jpg**:
    -   **分组**: 3个分组。
        1.  标题: `['店铺重要通知']`
        2.  正文段落: `['本店铺所有...', '产品详情...', '请联系...']`
        3.  按钮: `['确定收到']`
    -   **对齐**: 标题和按钮为 **居中对齐**，正文段落为 **左对齐**。
    -   **主布局**: 混合布局，但正文内容占据主要视觉，可视为 **主左对齐** 或 **混合对齐**。

-   **4.png**:
    -   **分组**: 这是一个典型的 **双列网格 (Grid)** 对比布局。
        -   顶部标签: `['加粗钢管', '约15mm']` (居中)
        -   左列: `['加粗升级款', '约15*15mm...', '稳稳承托...']` (内部居中)
        -   右列: `['常规薄款', '约10*10mm...', '框架薄...']` (内部居中)
    -   **对齐**: 各个子块内部均为 **居中对齐**。
    -   **主布局**: **网格/多列布局**，主对齐为 **居中**。

-   **5.jpg**:
    -   **分组**: 这是一个 **2x2 网格 (Grid)** 布局，外加一个顶部标题。
        -   标题: `['花样收纳', '想吸哪里吸哪里']` (居中)
        -   四个网格单元，每个单元内的文字都是居中的。
    -   **对齐**: 所有文本元素均为 **居中对齐**。
    -   **主布局**: **网格布局**，主对齐为 **居中**。

-   **6.jpg**:
    -   **分组**: 这是一个复杂的 **海报/分布式 (Distributed)** 布局，元素散布在各处。
        -   主标题 `['控油祛痘', '肌肤焕新']` 是一个视觉核心，为 **左对齐**。
        -   其他元素（品牌名、徽章、底部标语）有各自的对齐方式。
    -   **对齐**: 混合对齐。
    -   **主布局**: **分布式/复杂布局**，主对齐由左侧的大标题主导，应为 **主左对齐**。

-   **7.jpg**:
    -   **分组**: 2个主要分组。
        1.  顶部标题块: `['源自核心产区茶园', '每一颗...']`
        2.  底部段落块: `['选自高山...', '成长于...']`
    -   **对齐**: 顶部块为 **居中对齐**。底部块为 **左对齐**。
    -   **主布局**: 由顶部大号居中标题主导，应为 **主居中**。

-   **8.jpg**:
    -   **分组**: 3个独立的标签，分别指向不同物体。
    -   **对齐**: 无共同对齐轴，属于典型的 **分布式/标注 (Distributed)** 布局。
    -   **主布局**: **分布式布局**，主对齐为 **混合**。

-   **9.png**:
    -   **分组**: 3个分组。
        1.  顶部标题: `['我们用心，用户才放心']` (居中)
        2.  "小口袋"块: `['小口袋', '可放置...']` (左对齐)
        3.  "大口袋"块: `['大口袋', '可以放...']` (左对齐)
    -   **对齐**: 标题居中，内容块左对齐。
    -   **主布局**: 混合布局，主对齐可判为 **居中** (由标题引导) 或 **混合**。

## 当前算法问题诊断

当前 `LayoutAnalyzerV5` 算法虽然成功率100%（这可能意味着测试用例不够多样或成功标准过于宽松），但在布局模式和主对齐的判断上存在明显偏差：

1.  **对齐检测脆弱 (Critical)**:
    -   **问题**: `_get_block_alignment` 仅依赖标准差的最小值，对微小的像素抖动或OCR边界框误差极其敏感。
    -   **证据**: 图片 `10.png` (居中) 和 `3.jpg` (混合) 被错误识别为 `right` 对齐。这表明右边缘坐标的标准差偶然地比中心点坐标的标准差小了一点点，导致了误判。这是一个致命缺陷。

2.  **结构识别能力缺失 (Major)**:
    -   **问题**: 算法无法识别出 **表格** 和 **网格** 这两种非常重要的结构化布局。它将这些结构拆散成多个独立的 `multi_style_complex` 块。
    -   **证据**: 图片 `2.jpg` (表格), `4.png` (网格), `5.jpg` (网格) 均未被识别出其结构属性。`_detect_table_or_grid` 方法的逻辑（基于块中心的聚类）显然不够鲁棒，很容易被不规则的块大小和间距干扰。

3.  **主对齐逻辑有偏 (Moderate)**:
    -   **问题**: `_get_main_alignment` 的 "标题优先" 策略不够有效，且加权投票容易被大量次要文本干扰。
    -   **证据**:
        -   图片 `6.jpg` 中，左对齐的大标题是视觉焦点，但算法因其他小元素多而误判为 `center`。
        -   图片 `7.jpg` 中，居中的大标题被下方更大的左对齐段落的“权重”压倒，误判为 `left`。
    -   **根因**: 权重计算 `block.num_boxes * (block.avg_font_size ** 2)` 对行数多的块赋予了过高权重，而标题的定义和优先级的强度不足。

4.  **分组逻辑有待改进 (Minor)**:
    -   **问题**: `_build_graph` 中的字体大小相似性约束 `FONT_SIMILARITY_THRESH` 可能过于严格，导致表格中标题行和数据行无法被分到同一列的潜在组中，影响后续的结构分析。

## 优化策略

为解决上述问题，我将提出 `LayoutAnalyzerV6`，引入以下核心改进：

1.  **对齐检测鲁棒性增强**:
    -   **相对比较法**: 在 `_get_block_alignment` 中，不再简单地取 `np.std` 的最小值。一个对齐方式（如 `left`）必须**显著优于**其他方式（如 `center` 和 `right`）才会被采纳。例如，`std_left` 必须小于 `std_center * 0.75` 和 `std_right * 0.75`。如果几个对齐方式的标准差非常接近，则判定为 `mixed`，避免因噪声误判。

2.  **引入层级结构分析 (Hierarchical Analysis)**:
    -   **先聚行，再识列**: 优化分组逻辑。首先，使用更宽松的条件将紧邻的文本行聚合成“文本行组”。
    -   **列检测**: 基于这些“文本行组”的水平位置（`x1` 或 `cx`）进行聚类，以识别出清晰的列。这比在原始文本框上操作更稳定。

3.  **重构表格/网格检测器**:
    -   **基于网格单元分配**: 新的 `_detect_grid` 将不再依赖块中心聚类。
    -   **步骤**:
        1.  通过对所有文本框的 `x1, x2` 和 `y1, y2` 坐标进行一维聚类，确定出页面的“列边界”和“行边界”。
        2.  根据这些边界构建一个虚拟网格。
        3.  将每个文本块（TextBlock）分配到其中心点所在的网格单元 `(row, col)` 中。
        4.  通过分析网格的填充率、行数和列数来判断是否为 `table` 或 `grid` 布局。此方法对不规则间距和大小的鲁棒性要强得多。

4.  **强化主对齐判断**:
    -   **更强的标题优先**: 重新定义“标题”：位于页面顶部（如 `cy < page_h * 0.33`），且字体尺寸显著大于页面平均字体尺寸（如 `> 1.5 * avg_font`）。如果检测到这样的居中标题，**直接判定主对齐为 `center`**，拥有最高否决权。
    -   **改进的权重投票**: 如果没有强标题，则使用新的加权投票。权重公式改为 `weight = block.total_area * block.avg_font_size`。这更侧重于视觉上占据空间大、字体也大的块，比单纯依赖行数和字号平方更符合人类感知。

5.  **更精细的布局模式定义**:
    -   废弃模糊的 `multi_style_simple/complex`。
    -   引入更具描述性的模式: `table`, `grid`, `distributed`, `single_block`, `multi_block`。

## 优化后的算法实现

```python
import numpy as np
from collections import defaultdict
from itertools import groupby

class TextBox:
    """
    文本框类，与V5一致。
    """
    def __init__(self, bbox, text=""):
        self.x1, self.y1, self.x2, self.y2 = bbox
        self.text = text
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.cx = self.x1 + self.w / 2
        self.cy = self.y1 + self.h / 2
        self.area = self.w * self.h
        self.avg_char_w = self.w / len(self.text) if len(self.text) > 0 else self.h
        self.font_size = self.h 
        self.id = id(self)

    def __repr__(self):
        return f"Box(text='{self.text[:10]}...', bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"

    def horizontal_overlap(self, other):
        return max(0, min(self.x2, other.x2) - max(self.x1, other.x1))

class TextBlock:
    """
    文本块类，与V5一致。
    """
    def __init__(self, boxes):
        self.boxes = sorted(boxes, key=lambda b: b.y1)
        self.num_boxes = len(self.boxes)
        self.x1 = min(b.x1 for b in self.boxes) if self.boxes else 0
        self.y1 = min(b.y1 for b in self.boxes) if self.boxes else 0
        self.x2 = max(b.x2 for b in self.boxes) if self.boxes else 0
        self.y2 = max(b.y2 for b in self.boxes) if self.boxes else 0
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.cx = self.x1 + self.w / 2
        self.cy = self.y1 + self.h / 2
        self.total_area = sum(b.area for b in self.boxes)
        self.avg_font_size = np.mean([b.font_size for b in self.boxes]) if self.boxes else 0
        self.alignment = 'unknown'

    def __repr__(self):
        return f"TextBlock(boxes={self.num_boxes}, align='{self.alignment}', font={self.avg_font_size:.1f}, bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"


class LayoutAnalyzerV6:
    """
    布局分析算法 V6 - 结构感知与鲁棒性决策
    - 核心改进 (V6):
      1.  **鲁棒对齐检测**: 采用相对优势原则，避免噪声干扰。
      2.  **结构化布局识别**: 强大的表格/网格检测器，基于行列边界识别。
      3.  **智能主对齐**: 强化标题优先策略，并采用更符合视觉感知的加权投票。
      4.  **精细化布局模式**: 引入 'table', 'grid', 'distributed' 等更明确的模式。
    """
    def __init__(self, text_boxes,
                 v_dist_ratio=2.0,          # 垂直分组距离阈值
                 h_overlap_ratio=0.0,       # 垂直分组最小水平重叠率 (设为0，允许无重叠的列对齐)
                 align_std_thresh_ratio=0.15, # 对齐标准差阈值（倍于平均字符宽度）
                 align_relative_ superiority=0.75, # 对齐方式的相对优势阈值
                 font_similarity_thresh=0.7): # 分组时字体大小相似度阈值 (放宽)
        self.boxes = [TextBox(b, t) for b, t in text_boxes]
        self.page_w = max(b.x2 for b in self.boxes) if self.boxes else 1
        self.page_h = max(b.y2 for b in self.boxes) if self.boxes else 1
        
        # --- Tuned Parameters V6 ---
        self.V_DIST_RATIO = v_dist_ratio
        self.H_OVERLAP_RATIO = h_overlap_ratio
        self.ALIGN_STD_THRESH_RATIO = align_std_thresh_ratio
        self.ALIGN_RELATIVE_SUPERIORITY = align_relative_superiority
        self.FONT_SIMILARITY_THRESH = font_similarity_thresh

    def _build_graph(self):
        """图构建：逻辑与V5类似，但参数调整以适应更广泛的分组"""
        adj = defaultdict(list)
        if len(self.boxes) < 2: return adj

        for i in range(len(self.boxes)):
            for j in range(i + 1, len(self.boxes)):
                b1, b2 = self.boxes[i], self.boxes[j]
                avg_h = (b1.h + b2.h) / 2
                
                # 字体相似度约束 (放宽)
                if abs(b1.h - b2.h) > self.FONT_SIMILARITY_THRESH * avg_h:
                    continue

                # 垂直关系判断
                v_dist = max(0, max(b1.y1, b2.y1) - min(b1.y2, b2.y2))
                h_overlap = b1.horizontal_overlap(b2)
                
                # 条件：垂直距离近且有一定水平重叠（段落）或水平对齐（潜在列）
                is_vertically_close = v_dist < self.V_DIST_RATIO * avg_h
                is_horizontally_aligned = abs(b1.cx - b2.cx) < 2 * avg_h or abs(b1.x1 - b2.x1) < 2 * avg_h
                
                if is_vertically_close and (h_overlap > self.H_OVERLAP_RATIO * min(b1.w, b2.w) or is_horizontally_aligned):
                    adj[i].append(j)
                    adj[j].append(i)
        return adj

    def _find_blocks_with_graph(self):
        """使用图的连通分量寻找文本块"""
        if not self.boxes: return []
        adj = self._build_graph()
        visited = set()
        blocks = []
        for i in range(len(self.boxes)):
            if i not in visited:
                component_indices = []
                q = [i]; visited.add(i)
                head = 0
                while head < len(q):
                    u = q[head]; head += 1
                    component_indices.append(u)
                    for v in adj.get(u, []):
                        if v not in visited:
                            visited.add(v)
                            q.append(v)
                component_boxes = [self.boxes[k] for k in component_indices]
                blocks.append(TextBlock(component_boxes))
        return sorted(blocks, key=lambda blk: blk.y1)

    def _get_block_alignment(self, block):
        """V6 对齐检测: 鲁棒的相对优势法"""
        if block.num_boxes < 2:
            return 'single_line'

        boxes = block.boxes
        lefts = [b.x1 for b in boxes]
        centers = [b.cx for b in boxes]
        rights = [b.x2 for b in boxes]

        avg_char_w = np.mean([b.avg_char_w for b in boxes if b.avg_char_w > 0])
        if avg_char_w == 0: avg_char_w = np.mean([b.h for b in boxes])
        
        alignment_tolerance = self.ALIGN_STD_THRESH_RATIO * avg_char_w

        std_devs = {
            'left': np.std(lefts),
            'center': np.std(centers),
            'right': np.std(rights)
        }
        
        min_std = float('inf')
        best_align = 'mixed'
        
        # 找出标准差最小的对齐方式
        for align, std in std_devs.items():
            if std < min_std:
                min_std = std
                best_align = align

        # 如果最小标准差本身就很大，说明是对不齐的
        if min_std > alignment_tolerance:
            return 'mixed'

        # 相对优势判断：最佳对齐方式必须显著优于其他方式
        is_superior = True
        for align, std in std_devs.items():
            if align != best_align:
                if min_std > std * self.ALIGN_RELATIVE_SUPERIORITY:
                    is_superior = False
                    break
        
        return best_align if is_superior else 'mixed'

    def _cluster_1d(self, points, gap_threshold_ratio=0.2):
        """一维聚类，用于寻找行列边界"""
        if len(points) < 2:
            return [np.mean(points)] if points else []
        
        points = sorted(points)
        clusters = []
        current_cluster = [points[0]]
        
        # 动态间隙阈值
        overall_range = points[-1] - points[0]
        gap_threshold = max(overall_range * gap_threshold_ratio, 20) # 最小20px

        for i in range(1, len(points)):
            if points[i] - current_cluster[-1] < gap_threshold:
                current_cluster.append(points[i])
            else:
                clusters.append(np.mean(current_cluster))
                current_cluster = [points[i]]
        clusters.append(np.mean(current_cluster))
        return sorted(clusters)

    def _detect_grid(self, blocks, min_rows=2, min_cols=2, fill_ratio_thresh=0.6):
        """V6 网格/表格检测: 基于行列边界和单元分配"""
        if len(blocks) < min_rows * min_cols: return False

        # 1. 识别列和行的中心线
        col_centers = self._cluster_1d([b.cx for b in blocks])
        row_centers = self._cluster_1d([b.cy for b in blocks])

        num_cols, num_rows = len(col_centers), len(row_centers)

        if num_rows < min_rows or num_cols < min_cols:
            return False

        # 2. 构建网格并检查填充率
        grid = [[None for _ in range(num_cols)] for _ in range(num_rows)]
        filled_cells = 0
        
        # 使用最近邻分配块到网格单元
        for block in blocks:
            row_idx = np.argmin([abs(block.cy - r) for r in row_centers])
            col_idx = np.argmin([abs(block.cx - c) for c in col_centers])
            
            # 简单的分配，可以优化为只分配一次
            if grid[row_idx][col_idx] is None:
                grid[row_idx][col_idx] = block
                filled_cells += 1
        
        total_cells = num_rows * num_cols
        fill_ratio = filled_cells / total_cells if total_cells > 0 else 0

        return fill_ratio >= fill_ratio_thresh

    def _get_main_alignment(self, blocks):
        """V6 主对齐判断: 强化标题优先和视觉权重投票"""
        if not blocks: return "none"

        # 1. 强化标题优先策略
        avg_font_overall = np.mean([b.avg_font_size for b in blocks])
        for block in blocks:
            is_title = (block.cy < self.page_h * 0.33 and 
                        block.avg_font_size > 1.5 * avg_font_overall and
                        block.num_boxes <= 3) # 标题通常行数较少
            if is_title and block.alignment == 'center':
                return 'center' # 居中大标题有最高优先级

        # 2. 视觉加权投票
        alignment_scores = defaultdict(float)
        for block in blocks:
            if block.alignment not in ['mixed', 'single_line']:
                # 权重 = 视觉面积 * 字体大小 (更符合感知)
                weight = block.total_area * block.avg_font_size
                alignment_scores[block.alignment] += weight
        
        if alignment_scores:
            return max(alignment_scores, key=alignment_scores.get)
        
        # Fallback: 如果所有块都是混合或单行，则整体为混合
        return "mixed"

    def analyze(self):
        """执行完整的V6布局分析"""
        if not self.boxes:
            return {"layout_mode": "no_text", "main_alignment": "none", "blocks": []}

        blocks = self._find_blocks_with_graph()
        
        for block in blocks:
            block.alignment = self._get_block_alignment(block)

        # --- V6 最终布局决策 ---
        layout_mode = "unknown"
        if self._detect_grid(blocks, min_rows=2, min_cols=2, fill_ratio_thresh=0.6):
            layout_mode = "table"
        elif self._detect_grid(blocks, min_rows=2, min_cols=1, fill_ratio_thresh=0.8) or \
             self._detect_grid(blocks, min_rows=1, min_cols=2, fill_ratio_thresh=0.8):
             layout_mode = "grid" # 列表式网格
        else:
            # 分布式检测
            total_block_area = sum(b.w * b.h for b in blocks)
            if blocks:
                overall_x1 = min(b.x1 for b in blocks); overall_y1 = min(b.y1 for b in blocks)
                overall_x2 = max(b.x2 for b in blocks); overall_y2 = max(b.y2 for b in blocks)
                convex_hull_area = (overall_x2 - overall_x1) * (overall_y2 - overall_y1)
                if convex_hull_area > 0 and total_block_area / convex_hull_area < 0.4 and len(blocks) > 2:
                    layout_mode = "distributed"
            
        if layout_mode == "unknown":
            if len(blocks) == 1:
                layout_mode = "single_block"
            elif len(blocks) > 1:
                layout_mode = "multi_block"
            else:
                layout_mode = "no_text"

        main_alignment = self._get_main_alignment(blocks)

        return {
            "layout_mode": layout_mode,
            "main_alignment": main_alignment,
            "blocks": blocks
        }

# --- 示例使用 ---
if __name__ == '__main__':
    # 模拟图片2 (2.jpg) 的BBOX数据，这是一个表格布局
    # 注意：为了模拟真实OCR，坐标可能不完美
    boxes_data_img2 = [
        ([300, 100, 702, 120], '*产品尺寸为手工测量，以实际产品为准'),
        ([100, 200, 201, 220], '产品名称'), ([500, 201, 552, 221], '尺寸'),
        ([102, 230, 350, 250], '吸盘兔型湿厕巾收纳架'), ([498, 232, 703, 251], '卷纸款：15.3*6*24cm'),
        ([101, 260, 351, 280], '湿巾款：15.3*12.6*11cm'), # 额外加一行，测试表格
        ([100, 300, 200, 320], '产品材质'), ([501, 300, 550, 320], '颜色'),
        ([99, 330, 282, 350], '不锈钢+ABS'), ([500, 331, 623, 351], '胡桃木色'),
    ]

    print("--- V6 分析图片2 (表格) ---")
    analyzer2 = LayoutAnalyzerV6(boxes_data_img2)
    result2 = analyzer2.analyze()
    print(f"布局模式: {result2['layout_mode']}")
    print(f"主对齐: {result2['main_alignment']}")
    print(f"分组数量: {len(result2['blocks'])}")
    for i, block in enumerate(result2['blocks']):
        print(f"  Block {i+1}: {block}")

    print("\n" + "="*30 + "\n")

    # 模拟图片10 (10.png) 的BBOX数据，这是一个居中布局
    boxes_data_img10 = [
        ([302, 200, 698, 250], '可背也可提'),
        ([351, 260, 649, 300], '轻量便捷'),
        ([250, 350, 753, 390], '轻轻松松一提就走'),
    ]
    
    print("--- V6 分析图片10 (居中) ---")
    analyzer10 = LayoutAnalyzerV6(boxes_data_img10)
    result10 = analyzer10.analyze()
    print(f"布局模式: {result10['layout_mode']}")
    print(f"主对齐: {result10['main_alignment']}")
    print(f"分组数量: {len(result10['blocks'])}")
    for i, block in enumerate(result10['blocks']):
        print(f"  Block {i+1}: {block}")
```

## 预期改进效果

`LayoutAnalyzerV6` 算法通过引入结构感知和更鲁棒的决策逻辑，预期将带来以下显著改进：

1.  **对齐识别准确率大幅提升**:
    -   对于 `10.png` 和 `3.jpg`，新的相对优势对齐检测法将能抵抗噪声，正确识别出 `center` 和 `mixed/left`，而不再误判为 `right`。

2.  **结构化布局的正确识别**:
    -   对于 `2.jpg`, `4.png`, `5.jpg`，新的 `_detect_grid` 方法将能正确识别出它们的 `table` 或 `grid` 属性，这是V5完全缺失的关键能力。这将使布局模式的识别从“复杂”的模糊描述提升到“表格”的精确描述。

3.  **主对齐判断更符合人类视觉**:
    -   对于 `6.jpg`，强化的标题优先策略和新的视觉权重投票机制，将更可能将主对齐判定为由大标题主导的 `left`。
    -   对于 `7.jpg`，强化的标题优先策略将识别出顶部的居中大标题，并直接判定主对齐为 `center`，修正了V5的错误。

4.  **整体鲁棒性增强**:
    -   通过放宽部分阈值并引入更智能的判断逻辑，算法对不完美的OCR结果（边界框稍有偏差、字体大小识别不准）的容忍度更高。

5.  **输出结果更具价值**:
    -   新的布局模式（`table`, `grid`, `distributed`）为下游应用提供了更有价值的信息。例如，识别为 `table` 后，可以进一步提取行列数据；识别为 `distributed` 后，可以采用不同的渲染或分析策略。

综上所述，V6版本预计能修正当前版本中几乎所有的主要误判，并将布局分析的水平从简单的对齐检测提升到初步的结构理解。