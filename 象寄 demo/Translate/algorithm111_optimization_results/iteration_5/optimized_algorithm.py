"""
第5次迭代优化的布局识别算法
生成时间: 2025-07-14T23:25:21.455965
"""

import numpy as np
from collections import defaultdict
from itertools import groupby

class TextBox:
    """
    文本框类，与V5一致。
    """
    def __init__(self, bbox, text=""):
        self.x1, self.y1, self.x2, self.y2 = bbox
        self.text = text
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.cx = self.x1 + self.w / 2
        self.cy = self.y1 + self.h / 2
        self.area = self.w * self.h
        self.avg_char_w = self.w / len(self.text) if len(self.text) > 0 else self.h
        self.font_size = self.h 
        self.id = id(self)

    def __repr__(self):
        return f"Box(text='{self.text[:10]}...', bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"

    def horizontal_overlap(self, other):
        return max(0, min(self.x2, other.x2) - max(self.x1, other.x1))

class TextBlock:
    """
    文本块类，与V5一致。
    """
    def __init__(self, boxes):
        self.boxes = sorted(boxes, key=lambda b: b.y1)
        self.num_boxes = len(self.boxes)
        self.x1 = min(b.x1 for b in self.boxes) if self.boxes else 0
        self.y1 = min(b.y1 for b in self.boxes) if self.boxes else 0
        self.x2 = max(b.x2 for b in self.boxes) if self.boxes else 0
        self.y2 = max(b.y2 for b in self.boxes) if self.boxes else 0
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.cx = self.x1 + self.w / 2
        self.cy = self.y1 + self.h / 2
        self.total_area = sum(b.area for b in self.boxes)
        self.avg_font_size = np.mean([b.font_size for b in self.boxes]) if self.boxes else 0
        self.alignment = 'unknown'

    def __repr__(self):
        return f"TextBlock(boxes={self.num_boxes}, align='{self.alignment}', font={self.avg_font_size:.1f}, bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"


class LayoutAnalyzerV6:
    """
    布局分析算法 V6 - 结构感知与鲁棒性决策
    - 核心改进 (V6):
      1.  **鲁棒对齐检测**: 采用相对优势原则，避免噪声干扰。
      2.  **结构化布局识别**: 强大的表格/网格检测器，基于行列边界识别。
      3.  **智能主对齐**: 强化标题优先策略，并采用更符合视觉感知的加权投票。
      4.  **精细化布局模式**: 引入 'table', 'grid', 'distributed' 等更明确的模式。
    """
    def __init__(self, text_boxes,
                 v_dist_ratio=2.0,          # 垂直分组距离阈值
                 h_overlap_ratio=0.0,       # 垂直分组最小水平重叠率 (设为0，允许无重叠的列对齐)
                 align_std_thresh_ratio=0.15, # 对齐标准差阈值（倍于平均字符宽度）
                 align_relative_superiority=0.75, # 对齐方式的相对优势阈值
                 font_similarity_thresh=0.7): # 分组时字体大小相似度阈值 (放宽)
        self.boxes = [TextBox(b, t) for b, t in text_boxes]
        self.page_w = max(b.x2 for b in self.boxes) if self.boxes else 1
        self.page_h = max(b.y2 for b in self.boxes) if self.boxes else 1
        
        # --- Tuned Parameters V6 ---
        self.V_DIST_RATIO = v_dist_ratio
        self.H_OVERLAP_RATIO = h_overlap_ratio
        self.ALIGN_STD_THRESH_RATIO = align_std_thresh_ratio
        self.ALIGN_RELATIVE_SUPERIORITY = align_relative_superiority
        self.FONT_SIMILARITY_THRESH = font_similarity_thresh

    def _build_graph(self):
        """图构建：逻辑与V5类似，但参数调整以适应更广泛的分组"""
        adj = defaultdict(list)
        if len(self.boxes) < 2: return adj

        for i in range(len(self.boxes)):
            for j in range(i + 1, len(self.boxes)):
                b1, b2 = self.boxes[i], self.boxes[j]
                avg_h = (b1.h + b2.h) / 2
                
                # 字体相似度约束 (放宽)
                if abs(b1.h - b2.h) > self.FONT_SIMILARITY_THRESH * avg_h:
                    continue

                # 垂直关系判断
                v_dist = max(0, max(b1.y1, b2.y1) - min(b1.y2, b2.y2))
                h_overlap = b1.horizontal_overlap(b2)
                
                # 条件：垂直距离近且有一定水平重叠（段落）或水平对齐（潜在列）
                is_vertically_close = v_dist < self.V_DIST_RATIO * avg_h
                is_horizontally_aligned = abs(b1.cx - b2.cx) < 2 * avg_h or abs(b1.x1 - b2.x1) < 2 * avg_h
                
                if is_vertically_close and (h_overlap > self.H_OVERLAP_RATIO * min(b1.w, b2.w) or is_horizontally_aligned):
                    adj[i].append(j)
                    adj[j].append(i)
        return adj

    def _find_blocks_with_graph(self):
        """使用图的连通分量寻找文本块"""
        if not self.boxes: return []
        adj = self._build_graph()
        visited = set()
        blocks = []
        for i in range(len(self.boxes)):
            if i not in visited:
                component_indices = []
                q = [i]; visited.add(i)
                head = 0
                while head < len(q):
                    u = q[head]; head += 1
                    component_indices.append(u)
                    for v in adj.get(u, []):
                        if v not in visited:
                            visited.add(v)
                            q.append(v)
                component_boxes = [self.boxes[k] for k in component_indices]
                blocks.append(TextBlock(component_boxes))
        return sorted(blocks, key=lambda blk: blk.y1)

    def _get_block_alignment(self, block):
        """V6 对齐检测: 鲁棒的相对优势法"""
        if block.num_boxes < 2:
            return 'single_line'

        boxes = block.boxes
        lefts = [b.x1 for b in boxes]
        centers = [b.cx for b in boxes]
        rights = [b.x2 for b in boxes]

        avg_char_w = np.mean([b.avg_char_w for b in boxes if b.avg_char_w > 0])
        if avg_char_w == 0: avg_char_w = np.mean([b.h for b in boxes])
        
        alignment_tolerance = self.ALIGN_STD_THRESH_RATIO * avg_char_w

        std_devs = {
            'left': np.std(lefts),
            'center': np.std(centers),
            'right': np.std(rights)
        }
        
        min_std = float('inf')
        best_align = 'mixed'
        
        # 找出标准差最小的对齐方式
        for align, std in std_devs.items():
            if std < min_std:
                min_std = std
                best_align = align

        # 如果最小标准差本身就很大，说明是对不齐的
        if min_std > alignment_tolerance:
            return 'mixed'

        # 相对优势判断：最佳对齐方式必须显著优于其他方式
        is_superior = True
        for align, std in std_devs.items():
            if align != best_align:
                if min_std > std * self.ALIGN_RELATIVE_SUPERIORITY:
                    is_superior = False
                    break
        
        return best_align if is_superior else 'mixed'

    def _cluster_1d(self, points, gap_threshold_ratio=0.2):
        """一维聚类，用于寻找行列边界"""
        if len(points) < 2:
            return [np.mean(points)] if points else []
        
        points = sorted(points)
        clusters = []
        current_cluster = [points[0]]
        
        # 动态间隙阈值
        overall_range = points[-1] - points[0]
        gap_threshold = max(overall_range * gap_threshold_ratio, 20) # 最小20px

        for i in range(1, len(points)):
            if points[i] - current_cluster[-1] < gap_threshold:
                current_cluster.append(points[i])
            else:
                clusters.append(np.mean(current_cluster))
                current_cluster = [points[i]]
        clusters.append(np.mean(current_cluster))
        return sorted(clusters)

    def _detect_grid(self, blocks, min_rows=2, min_cols=2, fill_ratio_thresh=0.6):
        """V6 网格/表格检测: 基于行列边界和单元分配"""
        if len(blocks) < min_rows * min_cols: return False

        # 1. 识别列和行的中心线
        col_centers = self._cluster_1d([b.cx for b in blocks])
        row_centers = self._cluster_1d([b.cy for b in blocks])

        num_cols, num_rows = len(col_centers), len(row_centers)

        if num_rows < min_rows or num_cols < min_cols:
            return False

        # 2. 构建网格并检查填充率
        grid = [[None for _ in range(num_cols)] for _ in range(num_rows)]
        filled_cells = 0
        
        # 使用最近邻分配块到网格单元
        for block in blocks:
            row_idx = np.argmin([abs(block.cy - r) for r in row_centers])
            col_idx = np.argmin([abs(block.cx - c) for c in col_centers])
            
            # 简单的分配，可以优化为只分配一次
            if grid[row_idx][col_idx] is None:
                grid[row_idx][col_idx] = block
                filled_cells += 1
        
        total_cells = num_rows * num_cols
        fill_ratio = filled_cells / total_cells if total_cells > 0 else 0

        return fill_ratio >= fill_ratio_thresh

    def _get_main_alignment(self, blocks):
        """V6 主对齐判断: 强化标题优先和视觉权重投票"""
        if not blocks: return "none"

        # 1. 强化标题优先策略
        avg_font_overall = np.mean([b.avg_font_size for b in blocks])
        for block in blocks:
            is_title = (block.cy < self.page_h * 0.33 and 
                        block.avg_font_size > 1.5 * avg_font_overall and
                        block.num_boxes <= 3) # 标题通常行数较少
            if is_title and block.alignment == 'center':
                return 'center' # 居中大标题有最高优先级

        # 2. 视觉加权投票
        alignment_scores = defaultdict(float)
        for block in blocks:
            if block.alignment not in ['mixed', 'single_line']:
                # 权重 = 视觉面积 * 字体大小 (更符合感知)
                weight = block.total_area * block.avg_font_size
                alignment_scores[block.alignment] += weight
        
        if alignment_scores:
            return max(alignment_scores, key=alignment_scores.get)
        
        # Fallback: 如果所有块都是混合或单行，则整体为混合
        return "mixed"

    def analyze(self):
        """执行完整的V6布局分析"""
        if not self.boxes:
            return {"layout_mode": "no_text", "main_alignment": "none", "blocks": []}

        blocks = self._find_blocks_with_graph()
        
        for block in blocks:
            block.alignment = self._get_block_alignment(block)

        # --- V6 最终布局决策 ---
        layout_mode = "unknown"
        if self._detect_grid(blocks, min_rows=2, min_cols=2, fill_ratio_thresh=0.6):
            layout_mode = "table"
        elif self._detect_grid(blocks, min_rows=2, min_cols=1, fill_ratio_thresh=0.8) or \
             self._detect_grid(blocks, min_rows=1, min_cols=2, fill_ratio_thresh=0.8):
             layout_mode = "grid" # 列表式网格
        else:
            # 分布式检测
            total_block_area = sum(b.w * b.h for b in blocks)
            if blocks:
                overall_x1 = min(b.x1 for b in blocks); overall_y1 = min(b.y1 for b in blocks)
                overall_x2 = max(b.x2 for b in blocks); overall_y2 = max(b.y2 for b in blocks)
                convex_hull_area = (overall_x2 - overall_x1) * (overall_y2 - overall_y1)
                if convex_hull_area > 0 and total_block_area / convex_hull_area < 0.4 and len(blocks) > 2:
                    layout_mode = "distributed"
            
        if layout_mode == "unknown":
            if len(blocks) == 1:
                layout_mode = "single_block"
            elif len(blocks) > 1:
                layout_mode = "multi_block"
            else:
                layout_mode = "no_text"

        main_alignment = self._get_main_alignment(blocks)

        return {
            "layout_mode": layout_mode,
            "main_alignment": main_alignment,
            "blocks": blocks
        }

# --- 示例使用 ---
if __name__ == '__main__':
    # 模拟图片2 (2.jpg) 的BBOX数据，这是一个表格布局
    # 注意：为了模拟真实OCR，坐标可能不完美
    boxes_data_img2 = [
        ([300, 100, 702, 120], '*产品尺寸为手工测量，以实际产品为准'),
        ([100, 200, 201, 220], '产品名称'), ([500, 201, 552, 221], '尺寸'),
        ([102, 230, 350, 250], '吸盘兔型湿厕巾收纳架'), ([498, 232, 703, 251], '卷纸款：15.3*6*24cm'),
        ([101, 260, 351, 280], '湿巾款：15.3*12.6*11cm'), # 额外加一行，测试表格
        ([100, 300, 200, 320], '产品材质'), ([501, 300, 550, 320], '颜色'),
        ([99, 330, 282, 350], '不锈钢+ABS'), ([500, 331, 623, 351], '胡桃木色'),
    ]

    print("--- V6 分析图片2 (表格) ---")
    analyzer2 = LayoutAnalyzerV6(boxes_data_img2)
    result2 = analyzer2.analyze()
    print(f"布局模式: {result2['layout_mode']}")
    print(f"主对齐: {result2['main_alignment']}")
    print(f"分组数量: {len(result2['blocks'])}")
    for i, block in enumerate(result2['blocks']):
        print(f"  Block {i+1}: {block}")

    print("\n" + "="*30 + "\n")

    # 模拟图片10 (10.png) 的BBOX数据，这是一个居中布局
    boxes_data_img10 = [
        ([302, 200, 698, 250], '可背也可提'),
        ([351, 260, 649, 300], '轻量便捷'),
        ([250, 350, 753, 390], '轻轻松松一提就走'),
    ]
    
    print("--- V6 分析图片10 (居中) ---")
    analyzer10 = LayoutAnalyzerV6(boxes_data_img10)
    result10 = analyzer10.analyze()
    print(f"布局模式: {result10['layout_mode']}")
    print(f"主对齐: {result10['main_alignment']}")
    print(f"分组数量: {len(result10['blocks'])}")
    for i, block in enumerate(result10['blocks']):
        print(f"  Block {i+1}: {block}")
