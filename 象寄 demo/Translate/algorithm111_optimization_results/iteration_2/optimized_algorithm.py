"""
第2次迭代优化的布局识别算法
生成时间: 2025-07-14T23:15:58.269474
"""

import numpy as np
from collections import defaultdict

class TextBox:
    """
    增强的文本框类，包含更多几何和估算属性。
    """
    def __init__(self, bbox, text=""):
        self.x1, self.y1, self.x2, self.y2 = bbox
        self.text = text
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.cx = self.x1 + self.w / 2
        self.cy = self.y1 + self.h / 2
        self.area = self.w * self.h
        # 估算字体大小，用于更可靠的距离判断
        self.font_size = self.h 
        self.id = id(self)

    def __repr__(self):
        return f"Box(text='{self.text}', bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"

    def vertical_overlap(self, other):
        """计算与另一个框的垂直重叠度"""
        return max(0, min(self.y2, other.y2) - max(self.y1, other.y1))

class LayoutAnalyzerV3:
    """
    布局分析算法 V3
    - 核心改进:
      1. 行聚合 -> 块构建 的两阶段分组策略。
      2. 基于内点计数的稳健对齐检测。
      3. 引入表格(table)和分布式(distributed)布局模式的识别。
      4. 加权的主对齐决策机制。
    """
    def __init__(self, text_boxes,
                 line_v_overlap_thresh=0.5,
                 group_v_dist_ratio=1.5,
                 align_tolerance_ratio=0.05, # 容差为组宽的5%
                 align_inlier_thresh=0.51, # 至少51%的框符合才算对齐
                 table_min_cols=2,
                 dist_min_groups=3):
        self.boxes = sorted(text_boxes, key=lambda b: (b.y1, b.x1))
        self.LINE_V_OVERLAP_THRESH = line_v_overlap_thresh
        self.GROUP_V_DIST_RATIO = group_v_dist_ratio
        self.ALIGN_TOLERANCE_RATIO = align_tolerance_ratio
        self.ALIGN_INLIER_THRESH = align_inlier_thresh
        self.TABLE_MIN_COLS = table_min_cols
        self.DIST_MIN_GROUPS = dist_min_groups

    def _aggregate_lines(self):
        """第一阶段：将水平方向上邻近或重叠的框合并为“行”"""
        lines = []
        if not self.boxes:
            return lines

        visited = set()
        for i in range(len(self.boxes)):
            if self.boxes[i].id in visited:
                continue
            
            current_line_boxes = [self.boxes[i]]
            visited.add(self.boxes[i].id)
            
            # 向右查找同一行的其他框
            for j in range(i + 1, len(self.boxes)):
                if self.boxes[j].id in visited:
                    continue
                
                # 判断是否在同一行：垂直重叠度高
                v_overlap = self.boxes[i].vertical_overlap(self.boxes[j])
                if v_overlap / min(self.boxes[i].h, self.boxes[j].h) > self.LINE_V_OVERLAP_THRESH:
                    current_line_boxes.append(self.boxes[j])
                    visited.add(self.boxes[j].id)
            
            # 将行内框按x坐标排序
            current_line_boxes.sort(key=lambda b: b.x1)
            lines.append(current_line_boxes)
            
        # 按行的起始y坐标排序
        lines.sort(key=lambda line: line[0].y1)
        return lines

    def _get_alignment(self, group_boxes):
        """第二代对齐检测：基于内点计数，更稳健"""
        if len(group_boxes) < 2:
            return 'center' # 单行默认为居中

        group_width = max(b.x2 for b in group_boxes) - min(b.x1 for b in group_boxes)
        tolerance = group_width * self.ALIGN_TOLERANCE_RATIO

        # 使用中位数抵抗离群点
        median_left = np.median([b.x1 for b in group_boxes])
        median_right = np.median([b.x2 for b in group_boxes])
        median_center = np.median([b.cx for b in group_boxes])

        inliers_left = sum(1 for b in group_boxes if abs(b.x1 - median_left) < tolerance)
        inliers_right = sum(1 for b in group_boxes if abs(b.x2 - median_right) < tolerance)
        inliers_center = sum(1 for b in group_boxes if abs(b.cx - median_center) < tolerance)

        counts = {'left': inliers_left, 'right': inliers_right, 'center': inliers_center}
        
        # 找到最佳对齐方式
        best_align = max(counts, key=counts.get)
        
        # 如果最佳对齐的内点数未达到阈值，则认为是混合对齐
        if counts[best_align] < len(group_boxes) * self.ALIGN_INLIER_THRESH:
            return 'mixed'
            
        return best_align

    def _build_blocks(self, lines):
        """第二阶段：将行构建成块"""
        if not lines:
            return []

        blocks = []
        current_block_lines = [lines[0]]

        for i in range(1, len(lines)):
            prev_line_last_box = current_block_lines[-1][-1]
            current_line_first_box = lines[i][0]

            v_dist = current_line_first_box.y1 - prev_line_last_box.y2
            v_dist_threshold = self.GROUP_V_DIST_RATIO * max(prev_line_last_box.h, current_line_first_box.h)

            # 简单的水平重叠判断
            prev_line_x_range = (current_block_lines[-1][0].x1, current_block_lines[-1][-1].x2)
            curr_line_x_range = (lines[i][0].x1, lines[i][-1].x2)
            h_overlap = max(0, min(prev_line_x_range[1], curr_line_x_range[1]) - max(prev_line_x_range[0], curr_line_x_range[0]))

            if 0 <= v_dist < v_dist_threshold and h_overlap > 0:
                current_block_lines.append(lines[i])
            else:
                # 将多维列表展平
                blocks.append([box for line in current_block_lines for box in line])
                current_block_lines = [lines[i]]
        
        blocks.append([box for line in current_block_lines for box in line])
        return blocks

    def _detect_table_layout(self, analyzed_groups):
        """检测表格/多列布局"""
        if len(analyzed_groups) < self.TABLE_MIN_COLS:
            return False
        
        # 检查是否存在多个垂直对齐但水平分离的列
        # 简化版：检查是否存在多个左对齐且x位置不同的组
        left_aligned_groups = [g for g in analyzed_groups if g['alignment'] == 'left']
        if len(left_aligned_groups) < self.TABLE_MIN_COLS:
            return False

        # 检查这些左对齐组的水平位置是否有明显区分
        x_positions = sorted([g['boxes'][0].x1 for g in left_aligned_groups])
        gaps = [x_positions[i] - x_positions[i-1] for i in range(1, len(x_positions))]
        
        # 如果列之间的平均间隙大于列自身的平均宽度，则可能是表格
        avg_gap = np.mean(gaps) if gaps else 0
        avg_width = np.mean([max(b.x2 for b in g['boxes']) - min(b.x1 for b in g['boxes']) for g in left_aligned_groups])
        
        if avg_gap > avg_width * 0.5: # 间隙大于列宽的一半
            return True
        return False

    def _detect_distributed_layout(self, analyzed_groups):
        """检测分布式布局"""
        if len(analyzed_groups) < self.DIST_MIN_GROUPS:
            return False
        
        # 如果大部分组都是小尺寸的（比如只有1-2个框），则可能是分布式
        small_group_count = sum(1 for g in analyzed_groups if g['num_boxes'] <= 2)
        if small_group_count / len(analyzed_groups) >= 0.7: # 70%以上是小分组
            return True
        return False

    def analyze(self):
        """执行完整的V3布局分析"""
        if not self.boxes:
            return {"layout_mode": "no_text", "main_alignment": "none", "groups": []}

        lines = self._aggregate_lines()
        blocks = self._build_blocks(lines)
        
        analyzed_groups = []
        for block in blocks:
            if not block: continue
            alignment = self._get_alignment(block)
            total_area = sum(b.area for b in block)
            analyzed_groups.append({
                "alignment": alignment,
                "boxes": block,
                "num_boxes": len(block),
                "total_area": total_area
            })

        # --- 最终布局决策 ---
        layout_mode = "unknown"
        if self._detect_table_layout(analyzed_groups):
            layout_mode = "table"
        elif self._detect_distributed_layout(analyzed_groups):
            layout_mode = "distributed"
        else:
            unique_alignments = len(set(g['alignment'] for g in analyzed_groups))
            if len(analyzed_groups) <= 1 or unique_alignments == 1:
                layout_mode = "multi_style_simple"
            else:
                layout_mode = "multi_style_complex"

        # 判断主对齐方式（加权投票）
        alignment_scores = defaultdict(float)
        for group in analyzed_groups:
            # 权重 = 文本框数量 * 文本总面积 (给予内容多、面积大的组更高话语权)
            weight = group['num_boxes'] * group['total_area']
            alignment_scores[group['alignment']] += weight
        
        main_alignment = "none"
        if alignment_scores:
            # 忽略mixed，除非它是唯一的选择
            non_mixed_scores = {k: v for k, v in alignment_scores.items() if k != 'mixed'}
            if non_mixed_scores:
                main_alignment = max(non_mixed_scores, key=non_mixed_scores.get)
            else:
                main_alignment = max(alignment_scores, key=alignment_scores.get)

        return {
            "layout_mode": layout_mode,
            "main_alignment": main_alignment,
            "groups": analyzed_groups
        }

# --- 示例使用 ---
if __name__ == '__main__':
    # 模拟图片2 (2.jpg) 的BBOX数据，这是一个表格布局
    boxes_data_img2 = [
        TextBox([100, 200, 200, 220], '产品名称'),
        TextBox([100, 230, 350, 250], '吸盘兔型湿厕巾收纳架'),
        TextBox([100, 300, 200, 320], '产品材质'),
        TextBox([100, 330, 280, 350], '不锈钢+ABS'),
        TextBox([500, 200, 550, 220], '尺寸'),
        TextBox([500, 230, 700, 250], '卷纸款：15.3*6*24cm'),
        TextBox([500, 260, 720, 280], '湿巾款：15.3*12.6*11cm'),
        TextBox([500, 300, 550, 320], '颜色'),
        TextBox([500, 330, 620, 350], '胡桃木色'),
        TextBox([300, 100, 700, 120], '*产品尺寸为手工测量，以实际产品为准'), # 居中脚注
    ]

    print("--- V3 分析图片2 (2.jpg) ---")
    analyzer2 = LayoutAnalyzerV3(boxes_data_img2)
    result2 = analyzer2.analyze()
    print(f"布局模式: {result2['layout_mode']}")
    print(f"主对齐: {result2['main_alignment']}")
    print(f"分组数量: {len(result2['groups'])}")
    for i, group in enumerate(result2['groups']):
        print(f"  Group {i+1}: 对齐={group['alignment']}, 文本框数={group['num_boxes']}")

    print("\n" + "="*30 + "\n")

    # 模拟图片10 (10.png) 的BBOX数据，这是一个居中布局
    boxes_data_img10 = [
        TextBox([300, 200, 700, 250], '可背也可提'),
        TextBox([350, 260, 650, 300], '轻量便捷'),
        TextBox([250, 350, 750, 390], '轻轻松松一提就走'),
    ]
    
    print("--- V3 分析图片10 (10.png) ---")
    analyzer10 = LayoutAnalyzerV3(boxes_data_img10)
    result10 = analyzer10.analyze()
    print(f"布局模式: {result10['layout_mode']}")
    print(f"主对齐: {result10['main_alignment']}")
    print(f"分组数量: {len(result10['groups'])}")
    for i, group in enumerate(result10['groups']):
        print(f"  Group {i+1}: 对齐={group['alignment']}, 文本框数={group['num_boxes']}")
