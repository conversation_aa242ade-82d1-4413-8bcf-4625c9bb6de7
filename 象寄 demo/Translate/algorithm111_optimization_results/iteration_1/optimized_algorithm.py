"""
第1次迭代优化的布局识别算法
生成时间: 2025-07-14T23:12:59.916454
"""

import numpy as np
from collections import defaultdict

class TextBox:
    """一个辅助类，用于存储和计算文本框的属性。"""
    def __init__(self, bbox, text=""):
        # bbox: [x1, y1, x2, y2]
        self.x1, self.y1, self.x2, self.y2 = bbox
        self.text = text
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.cx = self.x1 + self.w / 2
        self.cy = self.y1 + self.h / 2
        self.area = self.w * self.h

    def __repr__(self):
        return f"Box(text='{self.text}', bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"

class LayoutAnalyzerV2:
    """
    优化的布局分析算法 V2
    - 采用分层策略：初始分组 -> 组内对齐分析 -> 最终决策
    - 核心是基于视觉连续性的分组和基于标准差的对齐检测
    """
    def __init__(self, text_boxes,
                 grouping_v_threshold_ratio=2.0,
                 grouping_h_overlap_ratio=0.2,
                 alignment_tolerance_ratio=0.5):
        """
        初始化分析器
        :param text_boxes: TextBox 对象的列表
        :param grouping_v_threshold_ratio: 垂直分组距离阈值与行高的比率
        :param grouping_h_overlap_ratio: 水平分组重叠度阈值
        :param alignment_tolerance_ratio: 对齐容差与平均字符宽度的比率
        """
        self.boxes = sorted(text_boxes, key=lambda b: (b.y1, b.x1))
        self.GROUPING_V_THRESHOLD_RATIO = grouping_v_threshold_ratio
        self.GROUPING_H_OVERLAP_RATIO = grouping_h_overlap_ratio
        self.ALIGNMENT_TOLERANCE_RATIO = alignment_tolerance_ratio

    def _get_alignment(self, group):
        """计算一个分组的对齐方式"""
        if len(group) < 2:
            # 单行文本无所谓对齐，但通常在设计中是居多
            return 'center'

        # 使用平均字符宽度作为动态容差的基准
        avg_char_width = np.mean([(b.w / len(b.text)) if len(b.text) > 0 else b.w for b in group])
        tolerance = avg_char_width * self.ALIGNMENT_TOLERANCE_RATIO

        left_edges = [b.x1 for b in group]
        right_edges = [b.x2 for b in group]
        centers = [b.cx for b in group]

        std_left = np.std(left_edges)
        std_right = np.std(right_edges)
        std_center = np.std(centers)

        min_std = min(std_left, std_right, std_center)

        if min_std > tolerance:
            return 'mixed'
        
        if std_left == min_std:
            return 'left'
        elif std_center == min_std:
            return 'center'
        else:
            return 'right'

    def _initial_grouping(self):
        """基于视觉连续性进行初始分组"""
        if not self.boxes:
            return []

        groups = []
        current_group = [self.boxes[0]]

        for i in range(1, len(self.boxes)):
            prev_box = current_group[-1]
            current_box = self.boxes[i]

            # 垂直距离阈值
            v_dist_threshold = self.GROUPING_V_THRESHOLD_RATIO * max(prev_box.h, current_box.h)
            v_dist = current_box.y1 - prev_box.y2

            # 水平重叠计算
            overlap_x1 = max(prev_box.x1, current_box.x1)
            overlap_x2 = min(prev_box.x2, current_box.x2)
            overlap_width = max(0, overlap_x2 - overlap_x1)
            
            # 判断是否属于同一组
            is_vertically_close = 0 <= v_dist < v_dist_threshold
            is_horizontally_overlapped = overlap_width > self.GROUPING_H_OVERLAP_RATIO * min(prev_box.w, current_box.w)

            if is_vertically_close and is_horizontally_overlapped:
                current_group.append(current_box)
            else:
                groups.append(current_group)
                current_group = [current_box]
        
        groups.append(current_group)
        return groups

    def analyze(self):
        """执行完整的布局分析"""
        initial_groups = self._initial_grouping()
        
        if not initial_groups:
            return {
                "layout_mode": "no_text",
                "main_alignment": "none",
                "groups": []
            }

        # 分析每个初始组的对齐方式
        analyzed_groups = []
        for group in initial_groups:
            alignment = self._get_alignment(group)
            total_area = sum(b.area for b in group)
            analyzed_groups.append({
                "alignment": alignment,
                "boxes": group,
                "num_boxes": len(group),
                "total_area": total_area
            })

        # 在此可以添加更复杂的组合并逻辑，但作为第一次迭代，我们先不合并
        # 而是直接基于分析后的组进行决策

        # 最终布局决策
        alignment_counts = defaultdict(int)
        alignment_area = defaultdict(float)
        for group in analyzed_groups:
            alignment_counts[group['alignment']] += 1
            alignment_area[group['alignment']] += group['total_area']

        num_groups = len(analyzed_groups)
        unique_alignments = len(alignment_counts)

        layout_mode = ""
        if num_groups <= 1 or unique_alignments == 1:
            layout_mode = "multi_style_simple" # 遵循原命名
        else:
            layout_mode = "multi_style_complex"

        # 判断主对齐方式（基于总面积）
        if not alignment_area:
            main_alignment = "none"
        else:
            main_alignment = max(alignment_area, key=alignment_area.get)

        # 特殊情况处理：分布式布局
        # 如果有很多小分组，且对齐方式混杂，则可能是分布式
        is_distributed = True
        if num_groups > 2:
            for group in analyzed_groups:
                if group['num_boxes'] > 1: # 如果存在多于1个框的组，则不太可能是分布式
                    is_distributed = False
                    break
            if is_distributed:
                layout_mode = "distributed"
                main_alignment = "mixed"
        
        return {
            "layout_mode": layout_mode,
            "main_alignment": main_alignment,
            "groups": analyzed_groups
        }

# --- 示例使用 ---
# 为了运行，我们需要模拟出文本框的BBOX数据。
# 这里以图片1为例，手动估算BBOX
# 注意：实际使用时，这些BBOX应由OCR引擎提供
if __name__ == '__main__':
    # 模拟图片1 (1.jpg) 的BBOX数据
    # 格式: [x1, y1, x2, y2]
    boxes_data_img1 = [
        TextBox([150, 50, 850, 120], '4D高回弹记忆棉'),
        TextBox([200, 130, 800, 190], '久睡不塌 适用更久'),
        TextBox([100, 700, 250, 730], '“0压感”'),
        TextBox([100, 740, 280, 770], '高回弹海绵'),
        TextBox([100, 800, 300, 830], '深度分散压力'),
        TextBox([100, 840, 250, 870], '不易塌陷'),
    ]

    print("--- 分析图片1 (1.jpg) ---")
    analyzer1 = LayoutAnalyzerV2(boxes_data_img1)
    result1 = analyzer1.analyze()
    print(f"布局模式: {result1['layout_mode']}")
    print(f"主对齐: {result1['main_alignment']}")
    print("分组详情:")
    for i, group in enumerate(result1['groups']):
        print(f"  Group {i+1}: 对齐={group['alignment']}, 文本框数={group['num_boxes']}")
        for box in group['boxes']:
            print(f"    - {box.text}")

    print("\n" + "="*30 + "\n")

    # 模拟图片10 (10.png) 的BBOX数据
    boxes_data_img10 = [
        TextBox([300, 200, 700, 250], '可背也可提'),
        TextBox([350, 260, 650, 300], '轻量便捷'),
        TextBox([250, 350, 750, 390], '轻轻松松一提就走'),
    ]
    
    print("--- 分析图片10 (10.png) ---")
    analyzer10 = LayoutAnalyzerV2(boxes_data_img10)
    result10 = analyzer10.analyze()
    print(f"布局模式: {result10['layout_mode']}")
    print(f"主对齐: {result10['main_alignment']}")
    print("分组详情:")
    for i, group in enumerate(result10['groups']):
        print(f"  Group {i+1}: 对齐={group['alignment']}, 文本框数={group['num_boxes']}")

    print("\n" + "="*30 + "\n")

    # 模拟图片8 (8.jpg) 的BBOX数据 (分布式)
    boxes_data_img8 = [
        TextBox([400, 100, 550, 140], '洗脸盆'),
        TextBox([600, 500, 750, 540], '洗衣盆'),
        TextBox([200, 600, 350, 640], '婴儿盆'),
    ]

    print("--- 分析图片8 (8.jpg) ---")
    analyzer8 = LayoutAnalyzerV2(boxes_data_img8)
    result8 = analyzer8.analyze()
    print(f"布局模式: {result8['layout_mode']}")
    print(f"主对齐: {result8['main_alignment']}")
    print("分组详情:")
    for i, group in enumerate(result10['groups']):
        print(f"  Group {i+1}: 对齐={group['alignment']}, 文本框数={group['num_boxes']}")
