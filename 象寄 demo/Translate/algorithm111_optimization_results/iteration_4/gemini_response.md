好的，作为一名专业的计算机视觉与布局分析算法专家，我将对您提供的数据和算法进行深入分析，并提出一套完整的优化方案和代码实现。

---

## 图片布局分析结果

以下是我对每张图片布局的专业人工分析，这将作为我们评估和优化的“基准真相”（Ground Truth）。

*   **1.jpg**: 混合布局。顶部是一个由两行文字组成的**居中对齐**块。底部是一个由两个小块组成的**左对齐**区域。主视觉焦点在顶部的居中标题。
    *   **真实分组**: 3个块 (顶部居中块, 中部左对齐块, 底部左对齐块)。
    *   **真实主对齐**: 居中 (由标题决定)。

*   **10.png**: 单块布局。所有三行文字（不含英文）构成一个逻辑整体，并且是清晰的**居中对齐**。
    *   **真实分组**: 1个块。
    *   **真实主对齐**: 居中。

*   **2.jpg**: 表格布局。这是一个典型的2列式表格，所有单元格内容均为**左对齐**。顶部有一个单独的、**居中对齐**的注释行。
    *   **真实分组**: 1个居中块 (注释) + 1个表格结构 (包含8个文本框，形成2x4的视觉效果)。
    *   **真实主对齐**: 左对齐 (由主体内容决定)。
    *   **真实布局模式**: 表格 (Table)。

*   **3.jpg**: 多块混合布局。顶部标题是**居中对齐**。中间段落是**左对齐**。底部按钮是**居中对齐**。
    *   **真实分组**: 3个块 (居中标题, 左对齐段落, 居中按钮)。
    *   **真实主对齐**: 混合 (没有单一主导对齐方式，但若必须选，居中或左对齐均有理由)。

*   **4.png**: 多列对比布局。这是一个双列布局，用于对比。左列和右列内部的文本块都是**居中对齐**的。顶部还有一个独立的**居中对齐**标签。
    *   **真实分组**: 3个主要块 (左列块, 右列块, 顶部标签块)，每个块内部都是居中对齐。
    *   **真实主对齐**: 居中。

*   **5.jpg**: 网格/分布式布局。顶部标题是**居中对齐**。下方的四个标签构成一个2x2的**网格布局**。
    *   **真实分组**: 1个居中标题块 + 4个分布式/网格元素。
    *   **真实主对齐**: 居中 (由标题和整体对称性决定)。
    *   **真实布局模式**: 网格 (Grid) / 分布式 (Distributed)。

*   **6.jpg**: 复杂海报/分布式布局。这是一个自由度非常高的“海报式”设计。主标题（控油祛痘...）是**左对齐**。其他元素（logo, 徽章, 底部文字）呈**分布式**。
    *   **真实分组**: 多个功能各异的块，难以简单归类。
    *   **真实主对齐**: 左对齐 (由最重要的标题块决定)。
    *   **真实布局模式**: 分布式 (Distributed) / 复杂 (Complex)。

*   **7.jpg**: 多块混合布局。顶部两个标题块是**居中对齐**。底部的段落块是**左对齐**。
    *   **真实分组**: 3个块 (居中大标题, 居中副标题, 左对齐段落)。
    *   **真实主对齐**: 居中 (由视觉上更突出的标题决定)。

*   **8.jpg**: 分布式/标注布局。三个标签分别指向不同物体，它们之间没有直接的对齐关系，是典型的**分布式**布局。
    *   **真实分组**: 3个独立的块。
    *   **真实主对齐**: 混合/无。
    *   **真实布局模式**: 分布式 (Distributed)。

*   **9.png**: 多块混合布局。顶部标题是**居中对齐**。下方左右两个图文块，其文字部分都是**左对齐**。
    *   **真实分组**: 3个块 (居中标题, 左侧图文块的文字部分, 右侧图文块的文字部分)。
    *   **真实主对齐**: 混合 (标题居中，内容左对齐)。

## 当前算法问题诊断

V4算法虽然引入了图聚类，但仍存在几个核心问题导致了误判：

1.  **对齐检测过于脆弱 (核心问题)**:
    *   `np.std` 对微小的OCR坐标误差非常敏感。对于 `10.png`，所有文本框视觉上居中，但其右边界(`x2`)的坐标可能因为字符宽度不同而有微小波动。如果这个波动的标准差恰好小于中心点(`cx`)坐标的标准差，就会被误判为右对齐。
    *   **案例**: `10.png` (居中 -> 右对齐), `3.jpg` (混合 -> 右对齐), `7.jpg` (居中 -> 左对齐) 均是此问题的体现。

2.  **分组逻辑不够精细**:
    *   `_build_graph` 的连接条件仅基于距离和重叠，没有考虑**字体大小、样式**等视觉一致性。这导致在 `6.jpg` 这样的复杂图中，许多不相关的元素被零散地分为一组，产生了`左对齐8组, 居中13组, 右对齐11组`这种碎片化的结果。
    *   对于段落，行间距稍大就可能导致图断开，将一个段落拆成多个块。

3.  **高级布局模式检测不足**:
    *   `_detect_table_layout` 过于简单，无法处理 `2.jpg` 这种带有标题行的真实表格。它仅基于`x1`聚类，鲁棒性差。
    *   `_detect_distributed_layout` 的凸包面积比率法，对于 `5.jpg` 这种规则的网格布局可能失效，因为网格本身形成的凸包很“满”，比率不够低。

4.  **主对齐判断逻辑有偏**:
    *   加权投票 `weight = block.num_boxes * np.sqrt(block.total_area)` 倾向于内容多、面积大的块。这导致在 `7.jpg` 中，面积较大的左对齐段落“战胜”了视觉上更重要的居中标题。在设计中，**标题的对齐方式往往定义了整个版面的基调**。

## 优化策略

基于以上诊断，我将提出 **LayoutAnalyzerV5**，核心优化点如下：

1.  **鲁棒的对齐检测 (`_get_block_alignment_v5`)**:
    *   **引入容忍度**: 不再单纯比较 `np.std` 的最小值。我们将定义一个 `alignment_tolerance` (基于平均字符宽度的百分比)，只有当 `std` 小于此容忍度时，才认为对齐有效。
    *   **相对比较**: 只有当一个对齐方式的 `std` **显著优于**其他两者时（例如，比第二名小50%以上），才采纳它。这避免了在模棱两可的情况下做出错误决策。
    *   **单行块处理**: 单行块本身无所谓内部对齐，其对齐方式应由其在页面上的位置决定。我们将单独标记并后续处理。

2.  **增强的分组逻辑 (`_build_graph_v5`)**:
    *   在图的边构建中，加入**字体大小相似性**作为关键条件。两个文本框只有在垂直相邻、水平重叠且**字体高度（`b.h`）相近**（例如，差异在30%以内）时，才被认为是强关联。这能有效防止将标题和正文错误地合并。
    *   适当放宽垂直距离阈值 (`V_DIST_RATIO`)，以更好地聚合行间距稍大的段落。

3.  **分层布局模式识别**:
    *   **重写表格检测 (`_detect_table_layout_v5`)**:
        1.  通过对文本框的 `cy` 和 `cx` 进行一维聚类，识别出清晰的行和列。
        2.  构建一个网格矩阵，统计每个单元格中的文本框数量。
        3.  如果该矩阵填充度高（例如，超过50%的单元格有内容），且至少有2行2列，则判定为表格。此方法对`2.jpg`非常有效。
    *   **引入网格检测 (`_detect_grid_layout_v5`)**: 专门用于检测 `5.jpg` 这样的规则网格。逻辑与表格检测类似，但条件更宽松，允许单元格内只有一个块。
    *   **优化主对齐判断 (`_get_main_alignment_v5`)**:
        1.  **标题优先**: 首先寻找“标题块”（通常是页面顶部、字体最大的块）。如果其为居中对齐，则**优先判定**主对齐为居中。
        2.  **内容主体**: 如果没有明显标题，则根据最大（按行数或面积）的非`mixed`对齐块来决定。
        3.  **加权投票作为后备**: 权重公式调整为 `weight = block.num_boxes * block.avg_font_size**2`，极大地增加了大号字体（标题）的影响力。

4.  **参数与阈值调优**:
    *   `ALIGN_STD_THRESH_RATIO`: 从基于高度改为基于**平均字符宽度**（更相关），并设定一个更严格的值。
    *   `V_DIST_RATIO`: 适当增大至 `2.5`，以连接段落。
    *   `FONT_SIMILARITY_THRESH`: 新增参数，用于分组，设为 `0.5` (50%的高度差异)。

## 优化后的算法实现

```python
import numpy as np
from collections import defaultdict
from itertools import groupby

class TextBox:
    """
    文本框类，与V4一致，但增加了ID以便于图操作。
    """
    def __init__(self, bbox, text=""):
        self.x1, self.y1, self.x2, self.y2 = bbox
        self.text = text
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.cx = self.x1 + self.w / 2
        self.cy = self.y1 + self.h / 2
        self.area = self.w * self.h
        # 估算平均字符宽度，对于对齐检测更鲁棒
        self.avg_char_w = self.w / len(self.text) if len(self.text) > 0 else self.h
        self.font_size = self.h 
        self.id = id(self)

    def __repr__(self):
        return f"Box(text='{self.text}', bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"

    def horizontal_overlap(self, other):
        """计算与另一个框的水平重叠度"""
        return max(0, min(self.x2, other.x2) - max(self.x1, other.x1))

class TextBlock:
    """
    表示一个由多个TextBox组成的视觉块。V5新增avg_font_size。
    """
    def __init__(self, boxes):
        self.boxes = sorted(boxes, key=lambda b: b.y1)
        self.num_boxes = len(self.boxes)
        self.x1 = min(b.x1 for b in self.boxes)
        self.y1 = min(b.y1 for b in self.boxes)
        self.x2 = max(b.x2 for b in self.boxes)
        self.y2 = max(b.y2 for b in self.boxes)
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.cx = self.x1 + self.w / 2
        self.cy = self.y1 + self.h / 2
        self.total_area = sum(b.area for b in self.boxes)
        self.avg_font_size = np.mean([b.font_size for b in self.boxes]) if self.boxes else 0
        self.alignment = 'unknown'

    def __repr__(self):
        return f"TextBlock(boxes={self.num_boxes}, alignment='{self.alignment}', avg_font={self.avg_font_size:.1f}, bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"


class LayoutAnalyzerV5:
    """
    布局分析算法 V5 - 鲁棒性和准确性增强版
    - 核心改进:
      1.  **鲁棒对齐检测**: 引入容忍度和相对比较，防止误判。
      2.  **智能分组**: 图构建时考虑字体大小相似性，避免错误合并。
      3.  **分层布局识别**: 强大的表格/网格检测，更精确的布局模式。
      4.  **智能主对齐**: 标题优先策略，更符合人类视觉感知。
    """
    def __init__(self, text_boxes,
                 v_dist_ratio=2.5,          # 垂直分组距离阈值（倍于行高），更宽松
                 h_overlap_ratio=0.1,       # 垂直分组的最小水平重叠率
                 align_std_thresh_ratio=0.1, # 对齐标准差阈值（倍于平均字符宽度），更严格
                 font_similarity_thresh=0.5): # 分组时字体大小相似度阈值
        self.boxes = [TextBox(b, t) for b, t in text_boxes]
        self.page_w = max(b.x2 for b in self.boxes) if self.boxes else 0
        self.page_h = max(b.y2 for b in self.boxes) if self.boxes else 0
        
        # --- Tuned Parameters ---
        self.V_DIST_RATIO = v_dist_ratio
        self.H_OVERLAP_RATIO = h_overlap_ratio
        self.ALIGN_STD_THRESH_RATIO = align_std_thresh_ratio
        self.FONT_SIMILARITY_THRESH = font_similarity_thresh

    def _build_graph(self):
        """V5图构建：加入字体相似度约束"""
        adj = defaultdict(list)
        if len(self.boxes) < 2:
            return adj

        for i in range(len(self.boxes)):
            for j in range(i + 1, len(self.boxes)):
                b1, b2 = self.boxes[i], self.boxes[j]
                avg_h = (b1.h + b2.h) / 2

                # 条件1: 字体大小相似
                if abs(b1.h - b2.h) > self.FONT_SIMILARITY_THRESH * avg_h:
                    continue

                # 条件2: 强垂直关系 (用于段落聚合)
                v_dist = max(0, max(b1.y1, b2.y1) - min(b1.y2, b2.y2))
                h_overlap = b1.horizontal_overlap(b2)
                if v_dist < self.V_DIST_RATIO * avg_h and h_overlap > self.H_OVERLAP_RATIO * min(b1.w, b2.w):
                    adj[i].append(j)
                    adj[j].append(i)
        return adj

    def _find_blocks_with_graph(self):
        """使用图的连通分量来寻找文本块 (与V4基本一致，但依赖于V5的图)"""
        if not self.boxes: return []
        adj = self._build_graph()
        visited = set()
        blocks = []
        for i in range(len(self.boxes)):
            if i not in visited:
                component_indices = []
                q = [i]; visited.add(i)
                head = 0
                while head < len(q):
                    u = q[head]; head += 1
                    component_indices.append(u)
                    for v in adj[u]:
                        if v not in visited:
                            visited.add(v)
                            q.append(v)
                component_boxes = [self.boxes[k] for k in component_indices]
                blocks.append(TextBlock(component_boxes))
        return sorted(blocks, key=lambda blk: blk.y1)

    def _get_block_alignment(self, block):
        """V5对齐检测：基于容忍度的鲁棒方法"""
        if block.num_boxes < 2:
            # 单行块的对齐由其在页面上的位置决定
            page_center_x = self.page_w / 2
            if abs(block.cx - page_center_x) < 0.1 * self.page_w:
                return 'center'
            elif block.x1 < 0.1 * self.page_w:
                return 'left'
            elif block.x2 > 0.9 * self.page_w:
                return 'right'
            return 'single_line_mixed' # 无法判断

        boxes = block.boxes
        lefts = [b.x1 for b in boxes]
        centers = [b.cx for b in boxes]
        rights = [b.x2 for b in boxes]

        avg_char_w = np.mean([b.avg_char_w for b in boxes if b.avg_char_w > 0])
        if avg_char_w == 0: avg_char_w = np.mean([b.h for b in boxes]) # Fallback
        
        # 容忍度：对齐线允许的抖动范围
        alignment_tolerance = self.ALIGN_STD_THRESH_RATIO * avg_char_w

        std_devs = {
            'left': np.std(lefts),
            'center': np.std(centers),
            'right': np.std(rights)
        }
        
        # 找出所有满足容忍度的对齐方式
        valid_alignments = {k: v for k, v in std_devs.items() if v < alignment_tolerance}

        if not valid_alignments:
            return 'mixed'
        
        # 从有效的对齐方式中选出标准差最小的那个
        best_align = min(valid_alignments, key=valid_alignments.get)
        return best_align

    def _cluster_1d(self, points, gap_ratio=0.5):
        """简单的一维聚类，用于行列识别"""
        if not points: return []
        points = sorted(points)
        clusters = []
        current_cluster = [points[0]]
        for i in range(1, len(points)):
            # 使用动态间隙阈值
            gap_threshold = gap_ratio * (current_cluster[-1] - current_cluster[0] + 1e-6)
            if points[i] - current_cluster[-1] < max(gap_threshold, 20): # 最小阈值20px
                current_cluster.append(points[i])
            else:
                clusters.append(np.mean(current_cluster))
                current_cluster = [points[i]]
        clusters.append(np.mean(current_cluster))
        return sorted(clusters)

    def _detect_table_or_grid(self, blocks, min_rows=2, min_cols=2, required_fill_ratio=0.5):
        """V5表格/网格检测：基于2D聚类"""
        if len(blocks) < min_rows * min_cols: return False

        # 1. 识别行和列的中心线
        col_centers = self._cluster_1d([b.cx for b in blocks])
        row_centers = self._cluster_1d([b.cy for b in blocks])

        if len(row_centers) < min_rows or len(col_centers) < min_cols:
            return False

        # 2. 构建网格并填充
        grid = [[None for _ in col_centers] for _ in row_centers]
        block_assigned = [False] * len(blocks)
        
        for i, r in enumerate(row_centers):
            for j, c in enumerate(col_centers):
                best_block_idx = -1
                min_dist = float('inf')
                for k, block in enumerate(blocks):
                    if not block_assigned[k]:
                        dist = np.sqrt((block.cy - r)**2 + (block.cx - c)**2)
                        if dist < min_dist and dist < (block.w + block.h): # 确保在范围内
                            min_dist = dist
                            best_block_idx = k
                if best_block_idx != -1:
                    grid[i][j] = blocks[best_block_idx]
                    block_assigned[best_block_idx] = True
        
        # 3. 检查填充率
        filled_cells = sum(cell is not None for row in grid for cell in row)
        total_cells = len(row_centers) * len(col_centers)
        
        if total_cells > 0 and filled_cells / total_cells >= required_fill_ratio:
            return True
        return False

    def _get_main_alignment(self, blocks):
        """V5主对齐判断：标题优先，权重优化"""
        if not blocks: return "none"

        # 1. 标题优先策略
        # 找到页面上半部分字体最大的块
        top_blocks = [b for b in blocks if b.cy < self.page_h * 0.4]
        if top_blocks:
            title_block = max(top_blocks, key=lambda b: b.avg_font_size)
            if title_block.alignment == 'center' and title_block.avg_font_size > np.mean([b.avg_font_size for b in blocks]):
                return 'center'

        # 2. 加权投票 (新权重)
        alignment_scores = defaultdict(float)
        for block in blocks:
            if block.alignment not in ['mixed', 'single_line_mixed']:
                weight = block.num_boxes * (block.avg_font_size ** 2)
                alignment_scores[block.alignment] += weight
        
        if alignment_scores:
            return max(alignment_scores, key=alignment_scores.get)
        
        return "mixed" # 如果所有块都是混合对齐

    def analyze(self):
        """执行完整的V5布局分析"""
        if not self.boxes:
            return {"layout_mode": "no_text", "main_alignment": "none", "blocks": []}

        blocks = self._find_blocks_with_graph()
        
        for block in blocks:
            block.alignment = self._get_block_alignment(block)

        # --- V5 最终布局决策 ---
        layout_mode = "unknown"
        if self._detect_table_or_grid(blocks, min_rows=2, min_cols=2, required_fill_ratio=0.6):
            layout_mode = "table"
        elif self._detect_table_or_grid(blocks, min_rows=2, min_cols=1, required_fill_ratio=0.7): # 允许单列网格
             layout_mode = "grid"
        else:
            # 沿用V4的分布式检测作为补充
            total_block_area = sum(b.w * b.h for b in blocks)
            overall_x1 = min(b.x1 for b in blocks); overall_y1 = min(b.y1 for b in blocks)
            overall_x2 = max(b.x2 for b in blocks); overall_y2 = max(b.y2 for b in blocks)
            convex_hull_area = (overall_x2 - overall_x1) * (overall_y2 - overall_y1)
            if convex_hull_area > 0 and total_block_area / convex_hull_area < 0.4 and len(blocks) > 2:
                layout_mode = "distributed"
            elif len(blocks) == 1:
                layout_mode = "single_block"
            else:
                layout_mode = "multi_block"

        main_alignment = self._get_main_alignment(blocks)

        return {
            "layout_mode": layout_mode,
            "main_alignment": main_alignment,
            "blocks": blocks
        }

# --- 示例使用 ---
if __name__ == '__main__':
    # 模拟图片2 (2.jpg) 的BBOX数据，这是一个表格布局
    boxes_data_img2 = [
        ([300, 100, 700, 120], '*产品尺寸为手工测量，以实际产品为准'),
        ([100, 200, 200, 220], '产品名称'), ([500, 200, 550, 220], '尺寸'),
        ([100, 230, 350, 250], '吸盘兔型湿厕巾收纳架'), ([500, 230, 700, 250], '卷纸款：15.3*6*24cm'),
        ([100, 300, 200, 320], '产品材质'), ([500, 300, 550, 320], '颜色'),
        ([100, 330, 280, 350], '不锈钢+ABS'), ([500, 330, 620, 350], '胡桃木色'),
    ]

    print("--- V5 分析图片2 (表格) ---")
    analyzer2 = LayoutAnalyzerV5(boxes_data_img2)
    result2 = analyzer2.analyze()
    print(f"布局模式: {result2['layout_mode']}")
    print(f"主对齐: {result2['main_alignment']}")
    print(f"分组数量: {len(result2['blocks'])}")
    for i, block in enumerate(result2['blocks']):
        print(f"  Block {i+1}: {block}")

    print("\n" + "="*30 + "\n")

    # 模拟图片10 (10.png) 的BBOX数据，这是一个居中布局
    boxes_data_img10 = [
        ([300, 200, 700, 250], '可背也可提'),
        ([350, 260, 650, 300], '轻量便捷'),
        ([250, 350, 750, 390], '轻轻松松一提就走'),
    ]
    
    print("--- V5 分析图片10 (居中) ---")
    analyzer10 = LayoutAnalyzerV5(boxes_data_img10)
    result10 = analyzer10.analyze()
    print(f"布局模式: {result10['layout_mode']}")
    print(f"主对齐: {result10['main_alignment']}")
    print(f"分组数量: {len(result2['blocks'])}")
    for i, block in enumerate(result10['blocks']):
        print(f"  Block {i+1}: {block}")
```

## 预期改进效果

1.  **对齐准确率大幅提升**:
    *   对于 `10.png` (居中) 和 `3.jpg` (混合)，V5的鲁棒对齐检测将不再因为微小的坐标波动而误判为“右对齐”，能正确识别出“居中”或“混合”。
    *   对于 `7.jpg`，V5的“标题优先”主对齐逻辑将正确地由顶部的居中标题决定主对齐为“居中”，而不是被下方的左对齐段落误导。

2.  **布局模式识别更精准**:
    *   `2.jpg` 将被V5的 `_detect_table_or_grid` 准确识别为 **`table`** 布局，而不是泛泛的 `multi_style_complex`。
    *   `5.jpg` 将被识别为 **`grid`** 布局，比 `multi_style_complex` 更具描述性。
    *   `8.jpg` 仍能被 `distributed` 模式捕获，展现了算法的向后兼容性。

3.  **分组质量更高**:
    *   对于 `6.jpg` 这样的复杂海报，通过引入字体大小相似性约束，V5能将视觉上属于同一层级（如主标题、副标题、说明文字）的内容更好地聚合在一起，避免生成大量无意义的微小碎片块。分组结果将更符合人类的视觉感知。

4.  **整体鲁棒性增强**:
    *   通过从依赖单一、脆弱的指标（如最小`std`）转向多维度、带容忍度的决策系统（如对齐容忍度、字体相似度、标题优先），V5算法对OCR误差、字体变化和复杂版式的容错能力将显著优于V4。

综上所述，**LayoutAnalyzerV5** 通过引入更符合人类视觉感知规则的启发式策略，预计能修正当前版本的所有主要错误，并将布局分析的准确性和粒度提升到一个新的水平。