#!/usr/bin/env python3
"""
测试Gemini优化的V5布局识别算法
使用真实图片数据验证算法性能
"""

import sys
import os
sys.path.append('../../')  # 添加项目根目录到路径

from optimized_algorithm import LayoutAnalyzerV5
from processors.ocr_processor import OCRProcessor
from models.data_models import PipelineConfig
import json

class V5AlgorithmTester:
    """V5算法实际测试器"""
    
    def __init__(self):
        self.ocr_processor = OCRProcessor()
        self.test_images = [
            "../../1.jpg",
            "../../2.jpg", 
            "../../3.jpg",
            "../../4.png",
            "../../5.jpg",
            "../../6.jpg",
            "../../7.jpg",
            "../../8.jpg",
            "../../9.png"
        ]
    
    def process_single_image(self, image_path):
        """处理单张图片并应用V5算法"""
        print(f"\n{'='*60}")
        print(f"🖼️  处理图片: {os.path.basename(image_path)}")
        print(f"{'='*60}")
        
        if not os.path.exists(image_path):
            print(f"❌ 图片不存在: {image_path}")
            return None
            
        try:
            # 1. OCR处理获取文本区域
            ocr_result = self.ocr_processor.process_image(image_path)
            if not ocr_result.success:
                print(f"❌ OCR处理失败: {ocr_result.error_message}")
                return None
                
            ocr_data = ocr_result.data
            chinese_regions = ocr_data.chinese_regions
            
            if not chinese_regions:
                print("⚠️  未检测到中文文本区域")
                return None
                
            print(f"📝 检测到 {len(chinese_regions)} 个中文文本区域")
            
            # 2. 转换为V5算法输入格式
            text_boxes = []
            for region in chinese_regions:
                bbox = region.bbox
                text = region.text
                text_boxes.append((bbox, text))
                print(f"   - '{text}' [{bbox[0]},{bbox[1]},{bbox[2]},{bbox[3]}]")
            
            # 3. 应用V5布局分析算法
            print(f"\n🔍 应用V5布局分析算法...")
            analyzer = LayoutAnalyzerV5(text_boxes)
            result = analyzer.analyze()
            
            # 4. 输出详细结果
            print(f"\n📊 V5算法分析结果:")
            print(f"   布局模式: {result['layout_mode']}")
            print(f"   主对齐方式: {result['main_alignment']}")
            print(f"   文本块数量: {len(result['blocks'])}")
            
            print(f"\n📋 详细分组结果:")
            for i, block in enumerate(result['blocks']):
                print(f"   Block {i+1}: {block.num_boxes}个文本框")
                print(f"     - 对齐方式: {block.alignment}")
                print(f"     - 平均字体: {block.avg_font_size:.1f}px")
                print(f"     - 区域范围: [{block.x1},{block.y1},{block.x2},{block.y2}]")
                print(f"     - 包含文本:")
                for box in block.boxes:
                    print(f"       * '{box.text}' (字体:{box.font_size}px)")
                print()
            
            return {
                'image_path': image_path,
                'ocr_regions_count': len(chinese_regions),
                'layout_result': result,
                'text_boxes': text_boxes
            }
            
        except Exception as e:
            print(f"❌ 处理过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def run_comprehensive_test(self):
        """运行全面测试"""
        print("🚀 开始V5算法全面测试")
        print("测试图片列表:", [os.path.basename(img) for img in self.test_images])
        
        results = []
        success_count = 0
        
        for image_path in self.test_images:
            result = self.process_single_image(image_path)
            if result:
                results.append(result)
                success_count += 1
        
        # 生成汇总报告
        print(f"\n{'='*60}")
        print(f"📈 V5算法测试汇总报告")
        print(f"{'='*60}")
        print(f"✅ 成功处理: {success_count}/{len(self.test_images)} 张图片")
        
        if results:
            # 统计分析
            layout_modes = [r['layout_result']['layout_mode'] for r in results]
            main_alignments = [r['layout_result']['main_alignment'] for r in results]
            avg_blocks = sum(len(r['layout_result']['blocks']) for r in results) / len(results)
            avg_regions = sum(r['ocr_regions_count'] for r in results) / len(results)
            
            print(f"\n📊 统计数据:")
            print(f"   平均文本区域数: {avg_regions:.1f}")
            print(f"   平均文本块数: {avg_blocks:.1f}")
            
            print(f"\n📋 布局模式分布:")
            layout_stats = {}
            for mode in layout_modes:
                layout_stats[mode] = layout_stats.get(mode, 0) + 1
            for mode, count in layout_stats.items():
                print(f"   {mode}: {count}次 ({count/len(results)*100:.1f}%)")
            
            print(f"\n📋 对齐方式分布:")
            alignment_stats = {}
            for alignment in main_alignments:
                alignment_stats[alignment] = alignment_stats.get(alignment, 0) + 1
            for alignment, count in alignment_stats.items():
                print(f"   {alignment}: {count}次 ({count/len(results)*100:.1f}%)")
        
        # 保存详细结果到JSON
        output_file = "v5_algorithm_test_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            # 简化results以便JSON序列化
            simplified_results = []
            for r in results:
                simplified = {
                    'image_path': r['image_path'],
                    'ocr_regions_count': r['ocr_regions_count'],
                    'layout_mode': r['layout_result']['layout_mode'],
                    'main_alignment': r['layout_result']['main_alignment'],
                    'blocks_count': len(r['layout_result']['blocks']),
                    'blocks_details': [
                        {
                            'num_boxes': block.num_boxes,
                            'alignment': block.alignment,
                            'avg_font_size': block.avg_font_size,
                            'bbox': [block.x1, block.y1, block.x2, block.y2],
                            'texts': [box.text for box in block.boxes]
                        }
                        for block in r['layout_result']['blocks']
                    ]
                }
                simplified_results.append(simplified)
            
            json.dump(simplified_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细结果已保存到: {output_file}")
        return results

def main():
    """主函数"""
    tester = V5AlgorithmTester()
    
    # 检查是否有命令行参数指定特定图片
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
        if not image_path.startswith('../../'):
            image_path = '../../' + image_path
        tester.process_single_image(image_path)
    else:
        # 运行全面测试
        tester.run_comprehensive_test()

if __name__ == "__main__":
    main() 