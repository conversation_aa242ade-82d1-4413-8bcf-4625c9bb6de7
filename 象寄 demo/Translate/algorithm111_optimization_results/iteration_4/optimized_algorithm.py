"""
第4次迭代优化的布局识别算法
生成时间: 2025-07-14T23:22:07.994439
"""

import numpy as np
from collections import defaultdict
from itertools import groupby

class TextBox:
    """
    文本框类，与V4一致，但增加了ID以便于图操作。
    """
    def __init__(self, bbox, text=""):
        self.x1, self.y1, self.x2, self.y2 = bbox
        self.text = text
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.cx = self.x1 + self.w / 2
        self.cy = self.y1 + self.h / 2
        self.area = self.w * self.h
        # 估算平均字符宽度，对于对齐检测更鲁棒
        self.avg_char_w = self.w / len(self.text) if len(self.text) > 0 else self.h
        self.font_size = self.h 
        self.id = id(self)

    def __repr__(self):
        return f"Box(text='{self.text}', bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"

    def horizontal_overlap(self, other):
        """计算与另一个框的水平重叠度"""
        return max(0, min(self.x2, other.x2) - max(self.x1, other.x1))

class TextBlock:
    """
    表示一个由多个TextBox组成的视觉块。V5新增avg_font_size。
    """
    def __init__(self, boxes):
        self.boxes = sorted(boxes, key=lambda b: b.y1)
        self.num_boxes = len(self.boxes)
        self.x1 = min(b.x1 for b in self.boxes)
        self.y1 = min(b.y1 for b in self.boxes)
        self.x2 = max(b.x2 for b in self.boxes)
        self.y2 = max(b.y2 for b in self.boxes)
        self.w = self.x2 - self.x1
        self.h = self.y2 - self.y1
        self.cx = self.x1 + self.w / 2
        self.cy = self.y1 + self.h / 2
        self.total_area = sum(b.area for b in self.boxes)
        self.avg_font_size = np.mean([b.font_size for b in self.boxes]) if self.boxes else 0
        self.alignment = 'unknown'

    def __repr__(self):
        return f"TextBlock(boxes={self.num_boxes}, alignment='{self.alignment}', avg_font={self.avg_font_size:.1f}, bbox=[{self.x1},{self.y1},{self.x2},{self.y2}])"


class LayoutAnalyzerV5:
    """
    布局分析算法 V5 - 鲁棒性和准确性增强版
    - 核心改进:
      1.  **鲁棒对齐检测**: 引入容忍度和相对比较，防止误判。
      2.  **智能分组**: 图构建时考虑字体大小相似性，避免错误合并。
      3.  **分层布局识别**: 强大的表格/网格检测，更精确的布局模式。
      4.  **智能主对齐**: 标题优先策略，更符合人类视觉感知。
    """
    def __init__(self, text_boxes,
                 v_dist_ratio=2.5,          # 垂直分组距离阈值（倍于行高），更宽松
                 h_overlap_ratio=0.1,       # 垂直分组的最小水平重叠率
                 align_std_thresh_ratio=0.1, # 对齐标准差阈值（倍于平均字符宽度），更严格
                 font_similarity_thresh=0.5): # 分组时字体大小相似度阈值
        self.boxes = [TextBox(b, t) for b, t in text_boxes]
        self.page_w = max(b.x2 for b in self.boxes) if self.boxes else 0
        self.page_h = max(b.y2 for b in self.boxes) if self.boxes else 0
        
        # --- Tuned Parameters ---
        self.V_DIST_RATIO = v_dist_ratio
        self.H_OVERLAP_RATIO = h_overlap_ratio
        self.ALIGN_STD_THRESH_RATIO = align_std_thresh_ratio
        self.FONT_SIMILARITY_THRESH = font_similarity_thresh

    def _build_graph(self):
        """V5图构建：加入字体相似度约束"""
        adj = defaultdict(list)
        if len(self.boxes) < 2:
            return adj

        for i in range(len(self.boxes)):
            for j in range(i + 1, len(self.boxes)):
                b1, b2 = self.boxes[i], self.boxes[j]
                avg_h = (b1.h + b2.h) / 2

                # 条件1: 字体大小相似
                if abs(b1.h - b2.h) > self.FONT_SIMILARITY_THRESH * avg_h:
                    continue

                # 条件2: 强垂直关系 (用于段落聚合)
                v_dist = max(0, max(b1.y1, b2.y1) - min(b1.y2, b2.y2))
                h_overlap = b1.horizontal_overlap(b2)
                if v_dist < self.V_DIST_RATIO * avg_h and h_overlap > self.H_OVERLAP_RATIO * min(b1.w, b2.w):
                    adj[i].append(j)
                    adj[j].append(i)
        return adj

    def _find_blocks_with_graph(self):
        """使用图的连通分量来寻找文本块 (与V4基本一致，但依赖于V5的图)"""
        if not self.boxes: return []
        adj = self._build_graph()
        visited = set()
        blocks = []
        for i in range(len(self.boxes)):
            if i not in visited:
                component_indices = []
                q = [i]; visited.add(i)
                head = 0
                while head < len(q):
                    u = q[head]; head += 1
                    component_indices.append(u)
                    for v in adj[u]:
                        if v not in visited:
                            visited.add(v)
                            q.append(v)
                component_boxes = [self.boxes[k] for k in component_indices]
                blocks.append(TextBlock(component_boxes))
        return sorted(blocks, key=lambda blk: blk.y1)

    def _get_block_alignment(self, block):
        """V5对齐检测：基于容忍度的鲁棒方法"""
        if block.num_boxes < 2:
            # 单行块的对齐由其在页面上的位置决定
            page_center_x = self.page_w / 2
            if abs(block.cx - page_center_x) < 0.1 * self.page_w:
                return 'center'
            elif block.x1 < 0.1 * self.page_w:
                return 'left'
            elif block.x2 > 0.9 * self.page_w:
                return 'right'
            return 'single_line_mixed' # 无法判断

        boxes = block.boxes
        lefts = [b.x1 for b in boxes]
        centers = [b.cx for b in boxes]
        rights = [b.x2 for b in boxes]

        avg_char_w = np.mean([b.avg_char_w for b in boxes if b.avg_char_w > 0])
        if avg_char_w == 0: avg_char_w = np.mean([b.h for b in boxes]) # Fallback
        
        # 容忍度：对齐线允许的抖动范围
        alignment_tolerance = self.ALIGN_STD_THRESH_RATIO * avg_char_w

        std_devs = {
            'left': np.std(lefts),
            'center': np.std(centers),
            'right': np.std(rights)
        }
        
        # 找出所有满足容忍度的对齐方式
        valid_alignments = {k: v for k, v in std_devs.items() if v < alignment_tolerance}

        if not valid_alignments:
            return 'mixed'
        
        # 从有效的对齐方式中选出标准差最小的那个
        best_align = min(valid_alignments, key=valid_alignments.get)
        return best_align

    def _cluster_1d(self, points, gap_ratio=0.5):
        """简单的一维聚类，用于行列识别"""
        if not points: return []
        points = sorted(points)
        clusters = []
        current_cluster = [points[0]]
        for i in range(1, len(points)):
            # 使用动态间隙阈值
            gap_threshold = gap_ratio * (current_cluster[-1] - current_cluster[0] + 1e-6)
            if points[i] - current_cluster[-1] < max(gap_threshold, 20): # 最小阈值20px
                current_cluster.append(points[i])
            else:
                clusters.append(np.mean(current_cluster))
                current_cluster = [points[i]]
        clusters.append(np.mean(current_cluster))
        return sorted(clusters)

    def _detect_table_or_grid(self, blocks, min_rows=2, min_cols=2, required_fill_ratio=0.5):
        """V5表格/网格检测：基于2D聚类"""
        if len(blocks) < min_rows * min_cols: return False

        # 1. 识别行和列的中心线
        col_centers = self._cluster_1d([b.cx for b in blocks])
        row_centers = self._cluster_1d([b.cy for b in blocks])

        if len(row_centers) < min_rows or len(col_centers) < min_cols:
            return False

        # 2. 构建网格并填充
        grid = [[None for _ in col_centers] for _ in row_centers]
        block_assigned = [False] * len(blocks)
        
        for i, r in enumerate(row_centers):
            for j, c in enumerate(col_centers):
                best_block_idx = -1
                min_dist = float('inf')
                for k, block in enumerate(blocks):
                    if not block_assigned[k]:
                        dist = np.sqrt((block.cy - r)**2 + (block.cx - c)**2)
                        if dist < min_dist and dist < (block.w + block.h): # 确保在范围内
                            min_dist = dist
                            best_block_idx = k
                if best_block_idx != -1:
                    grid[i][j] = blocks[best_block_idx]
                    block_assigned[best_block_idx] = True
        
        # 3. 检查填充率
        filled_cells = sum(cell is not None for row in grid for cell in row)
        total_cells = len(row_centers) * len(col_centers)
        
        if total_cells > 0 and filled_cells / total_cells >= required_fill_ratio:
            return True
        return False

    def _get_main_alignment(self, blocks):
        """V5主对齐判断：标题优先，权重优化"""
        if not blocks: return "none"

        # 1. 标题优先策略
        # 找到页面上半部分字体最大的块
        top_blocks = [b for b in blocks if b.cy < self.page_h * 0.4]
        if top_blocks:
            title_block = max(top_blocks, key=lambda b: b.avg_font_size)
            if title_block.alignment == 'center' and title_block.avg_font_size > np.mean([b.avg_font_size for b in blocks]):
                return 'center'

        # 2. 加权投票 (新权重)
        alignment_scores = defaultdict(float)
        for block in blocks:
            if block.alignment not in ['mixed', 'single_line_mixed']:
                weight = block.num_boxes * (block.avg_font_size ** 2)
                alignment_scores[block.alignment] += weight
        
        if alignment_scores:
            return max(alignment_scores, key=alignment_scores.get)
        
        return "mixed" # 如果所有块都是混合对齐

    def analyze(self):
        """执行完整的V5布局分析"""
        if not self.boxes:
            return {"layout_mode": "no_text", "main_alignment": "none", "blocks": []}

        blocks = self._find_blocks_with_graph()
        
        for block in blocks:
            block.alignment = self._get_block_alignment(block)

        # --- V5 最终布局决策 ---
        layout_mode = "unknown"
        if self._detect_table_or_grid(blocks, min_rows=2, min_cols=2, required_fill_ratio=0.6):
            layout_mode = "table"
        elif self._detect_table_or_grid(blocks, min_rows=2, min_cols=1, required_fill_ratio=0.7): # 允许单列网格
             layout_mode = "grid"
        else:
            # 沿用V4的分布式检测作为补充
            total_block_area = sum(b.w * b.h for b in blocks)
            overall_x1 = min(b.x1 for b in blocks); overall_y1 = min(b.y1 for b in blocks)
            overall_x2 = max(b.x2 for b in blocks); overall_y2 = max(b.y2 for b in blocks)
            convex_hull_area = (overall_x2 - overall_x1) * (overall_y2 - overall_y1)
            if convex_hull_area > 0 and total_block_area / convex_hull_area < 0.4 and len(blocks) > 2:
                layout_mode = "distributed"
            elif len(blocks) == 1:
                layout_mode = "single_block"
            else:
                layout_mode = "multi_block"

        main_alignment = self._get_main_alignment(blocks)

        return {
            "layout_mode": layout_mode,
            "main_alignment": main_alignment,
            "blocks": blocks
        }

# --- 示例使用 ---
if __name__ == '__main__':
    # 模拟图片2 (2.jpg) 的BBOX数据，这是一个表格布局
    boxes_data_img2 = [
        ([300, 100, 700, 120], '*产品尺寸为手工测量，以实际产品为准'),
        ([100, 200, 200, 220], '产品名称'), ([500, 200, 550, 220], '尺寸'),
        ([100, 230, 350, 250], '吸盘兔型湿厕巾收纳架'), ([500, 230, 700, 250], '卷纸款：15.3*6*24cm'),
        ([100, 300, 200, 320], '产品材质'), ([500, 300, 550, 320], '颜色'),
        ([100, 330, 280, 350], '不锈钢+ABS'), ([500, 330, 620, 350], '胡桃木色'),
    ]

    print("--- V5 分析图片2 (表格) ---")
    analyzer2 = LayoutAnalyzerV5(boxes_data_img2)
    result2 = analyzer2.analyze()
    print(f"布局模式: {result2['layout_mode']}")
    print(f"主对齐: {result2['main_alignment']}")
    print(f"分组数量: {len(result2['blocks'])}")
    for i, block in enumerate(result2['blocks']):
        print(f"  Block {i+1}: {block}")

    print("\n" + "="*30 + "\n")

    # 模拟图片10 (10.png) 的BBOX数据，这是一个居中布局
    boxes_data_img10 = [
        ([300, 200, 700, 250], '可背也可提'),
        ([350, 260, 650, 300], '轻量便捷'),
        ([250, 350, 750, 390], '轻轻松松一提就走'),
    ]
    
    print("--- V5 分析图片10 (居中) ---")
    analyzer10 = LayoutAnalyzerV5(boxes_data_img10)
    result10 = analyzer10.analyze()
    print(f"布局模式: {result10['layout_mode']}")
    print(f"主对齐: {result10['main_alignment']}")
    print(f"分组数量: {len(result2['blocks'])}")
    for i, block in enumerate(result10['blocks']):
        print(f"  Block {i+1}: {block}")
