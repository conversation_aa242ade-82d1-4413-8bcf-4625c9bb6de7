#!/usr/bin/env python3
"""
Gemini 样式分组算法优化器
使用多张图片样本让 Gemini 分析并优化文字样式分组算法
支持多轮迭代优化

使用方式:
    python gemini_algorithm_optimizer.py                    # 交互模式，每轮询问是否继续
    python gemini_algorithm_optimizer.py --single           # 只运行一次优化
    python gemini_algorithm_optimizer.py --auto -i 5        # 自动模式，连续运行5轮
    python gemini_algorithm_optimizer.py -I -i 3            # 交互模式，最多3轮
    python gemini_algorithm_optimizer.py -c                 # 使用用户修正的分组数据进行优化
    python gemini_algorithm_optimizer.py -c --single        # 单次优化，使用修正分组数据
    python gemini_algorithm_optimizer.py -c --auto -i 3     # 自动模式，使用修正分组数据

特性:
    📊 自动分析当前样式分组算法性能
    🤖 调用 Gemini 2.5 Pro 进行智能优化
    🔄 支持多轮迭代改进，从简单规则到智能聚类
    👤 交互模式让您控制优化进程
    📁 完整保存每轮结果和分析报告
    🎨 专注于字号、颜色的视觉样式分组
    🎯 支持基于用户修正分组数据的精确优化
"""

import os
import sys
import json
import base64
import requests
import cv2
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from processors.ocr_processor import OCRProcessor
from processors.layout_processor import LayoutProcessor
from config.settings import ConfigManager

class GeminiStyleGroupingOptimizer:
    """Gemini 样式分组算法优化器"""
    
    def __init__(self):
        self.api_key = "AIzaSyA8qlBZbbXzyWiSbbUns_bBIvvRUy5Uc4k"
        self.api_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent"
        self.model = "gemini-2.5-pro"
        
        # 初始化处理器
        self.config_manager = ConfigManager()
        self.ocr_processor = OCRProcessor()
        self.layout_processor = LayoutProcessor()
        
        # 设置输出目录
        self.output_dir = "algorithm_optimization_results"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 图片目录
        self.images_dir = "gemini_images"
        
        # 迭代历史
        self.iteration_history = []
        
        print(f"🚀 Gemini样式分组算法优化器初始化完成")
        print(f"📊 使用模型: {self.model}")
        print(f"📁 图片目录: {self.images_dir}")
        print(f"💾 输出目录: {self.output_dir}")
    
    def cleanup(self):
        """清理资源"""
        if hasattr(self, 'ocr_processor'):
            self.ocr_processor.cleanup()
        print("🧹 优化器资源清理完成")
    
    def calculate_rgb_distance(self, color1: Tuple[int, int, int], color2: Tuple[int, int, int]) -> float:
        """计算两个RGB颜色之间的欧几里得距离"""
        return np.sqrt(sum((c1 - c2) ** 2 for c1, c2 in zip(color1, color2)))
    
    def strict_style_grouping(self, regions: List[Any]) -> List[List[Any]]:
        """
        严格样式分组：精确高度相同 + 颜色完全相同
        """
        groups = []
        for region in regions:
            # 寻找完全匹配的现有组
            matched = False
            for group in groups:
                if (hasattr(group[0], 'style_info') and hasattr(region, 'style_info') and
                    group[0].style_info and region.style_info):
                    ref_height = group[0].style_info.get('precise_height', 0)
                    ref_color = group[0].style_info.get('text_color', (0, 0, 0))
                    cur_height = region.style_info.get('precise_height', 0)
                    cur_color = region.style_info.get('text_color', (0, 0, 0))
                    
                    if (ref_height == cur_height and ref_color == cur_color):
                        group.append(region)
                        matched = True
                        break
            if not matched:
                groups.append([region])
        return groups
    
    def tolerance_style_grouping(self, regions: List[Any], height_tolerance: int = 2, color_tolerance: float = 15) -> List[List[Any]]:
        """
        容忍度样式分组：高度±tolerance + 颜色RGB距离<threshold
        """
        groups = []
        for region in regions:
            matched = False
            for group in groups:
                if (hasattr(group[0], 'style_info') and hasattr(region, 'style_info') and
                    group[0].style_info and region.style_info):
                    ref_height = group[0].style_info.get('precise_height', 0)
                    ref_color = group[0].style_info.get('text_color', (0, 0, 0))
                    cur_height = region.style_info.get('precise_height', 0)
                    cur_color = region.style_info.get('text_color', (0, 0, 0))
                    
                    height_diff = abs(ref_height - cur_height)
                    color_diff = self.calculate_rgb_distance(ref_color, cur_color)
                    
                    if height_diff <= height_tolerance and color_diff <= color_tolerance:
                        group.append(region)
                        matched = True
                        break
            if not matched:
                groups.append([region])
        return groups
    
    def calculate_height_variance(self, group: List[Any]) -> float:
        """计算组内高度方差"""
        heights = []
        for region in group:
            if hasattr(region, 'style_info') and region.style_info:
                height = region.style_info.get('precise_height', 0)
                if height > 0:
                    heights.append(height)
        if len(heights) <= 1:
            return 0.0
        mean_height = sum(heights) / len(heights)
        variance = sum((h - mean_height) ** 2 for h in heights) / len(heights)
        return variance
    
    def calculate_color_variance(self, group: List[Any]) -> float:
        """计算组内颜色方差"""
        colors = []
        for region in group:
            if hasattr(region, 'style_info') and region.style_info:
                color = region.style_info.get('text_color', (0, 0, 0))
                colors.append(color)
        if len(colors) <= 1:
            return 0.0
        
        # 计算RGB各通道的方差
        r_values = [c[0] for c in colors]
        g_values = [c[1] for c in colors]  
        b_values = [c[2] for c in colors]
        
        r_var = np.var(r_values) if len(r_values) > 1 else 0
        g_var = np.var(g_values) if len(g_values) > 1 else 0
        b_var = np.var(b_values) if len(b_values) > 1 else 0
        
        # 返回RGB方差的均值
        return (r_var + g_var + b_var) / 3
    
    def visualize_style_groups(self, image_path: str, groups: List[List[Any]], output_dir: str) -> None:
        """可视化样式分组结果"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                return
            
            # 定义组颜色
            colors = [
                (255, 0, 0),    # 红色
                (0, 255, 0),    # 绿色
                (0, 0, 255),    # 蓝色
                (255, 255, 0),  # 黄色
                (255, 0, 255),  # 紫色
                (0, 255, 255),  # 青色
                (128, 0, 128),  # 紫红色
                (255, 165, 0),  # 橙色
                (128, 128, 128), # 灰色
                (0, 128, 0),    # 深绿色
            ]
            
            # 为每个组绘制边框
            for i, group in enumerate(groups):
                color = colors[i % len(colors)]
                for region in group:
                    if hasattr(region, 'bbox') and region.bbox:
                        x, y, w, h = region.bbox
                        # 绘制组边框
                        cv2.rectangle(image, (x, y), (x + w, y + h), color, 2)
                        # 标注组号
                        cv2.putText(image, f"G{i+1}", (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # 保存可视化结果
            output_file = os.path.join(output_dir, f"style_grouping_{os.path.basename(image_path)}")
            cv2.imwrite(output_file, image)
            print(f"  📊 样式分组可视化已保存: {output_file}")
            
        except Exception as e:
            print(f"  ❌ 生成可视化失败: {e}")
    
    def encode_image_to_base64(self, image_path: str) -> str:
        """将图片编码为base64"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def get_image_files(self) -> List[str]:
        """获取所有图片文件"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        image_files = []
        
        for file in os.listdir(self.images_dir):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(self.images_dir, file))
        
        # 按文件名排序
        image_files.sort()
        return image_files
    
    def analyze_current_grouping_performance(self, image_files: List[str], 
                                           algorithm_type: str = "tolerance", 
                                           custom_algorithm_code: str = None) -> Dict[str, Any]:
        """分析当前样式分组算法在所有图片上的表现"""
        print(f"\n📊 分析样式分组算法性能 - {len(image_files)} 张图片")
        
        # 如果提供了自定义算法代码，尝试动态加载
        custom_grouping_func = None
        if custom_algorithm_code:
            try:
                # 动态执行Gemini优化的算法代码
                exec_globals = {
                    'math': __import__('math'),
                    'np': __import__('numpy'),
                    'collections': __import__('collections'),
                    'List': List,
                    'Dict': Dict,
                    'Any': Any
                }
                exec(custom_algorithm_code, exec_globals)
                
                # 查找自定义的分组函数
                if 'optimized_style_grouping' in exec_globals:
                    custom_grouping_func = exec_globals['optimized_style_grouping']
                    print(f"✅ 已加载Gemini优化的算法代码")
                elif 'group_text_regions' in exec_globals:
                    custom_grouping_func = exec_globals['group_text_regions']
                    print(f"✅ 已加载Gemini优化的算法代码 (group_text_regions)")
                elif 'group_text_styles_optimized' in exec_globals:
                    custom_grouping_func = exec_globals['group_text_styles_optimized']
                    print(f"✅ 已加载Gemini优化的算法代码 (group_text_styles_optimized)")
                elif 'TextStyleGrouper' in exec_globals:
                    # 处理类形式的算法
                    grouper_class = exec_globals['TextStyleGrouper']
                    grouper_instance = grouper_class()
                    if hasattr(grouper_instance, 'group_text_regions'):
                        custom_grouping_func = grouper_instance.group_text_regions
                        print(f"✅ 已加载Gemini优化的算法代码 (TextStyleGrouper类)")
                    else:
                        print(f"⚠️ TextStyleGrouper类中未找到group_text_regions方法")
                else:
                    print(f"⚠️ 无法在算法代码中找到标准分组函数，使用默认算法")
                    print(f"📋 可用函数: {[k for k in exec_globals.keys() if not k.startswith('_') and callable(exec_globals.get(k, None))]}")
            except Exception as e:
                print(f"❌ 加载自定义算法失败: {e}")
                print(f"🔄 回退使用默认算法")
        
        algorithm_name = "Gemini优化算法" if custom_grouping_func else algorithm_type
        print(f"🔧 使用算法: {algorithm_name}")
        
        performance_data = {
            "algorithm_type": algorithm_name,
            "total_images": len(image_files),
            "successful_analyses": 0,
            "failed_analyses": 0,
            "image_results": [],
            "grouping_statistics": {
                "total_groups": 0,
                "single_text_groups": 0,
                "multi_text_groups": 0,
                "max_group_size": 0,
                "average_group_size": 0.0
            }
        }
        
        for i, image_path in enumerate(image_files, 1):
            print(f"  分析图片 {i}/{len(image_files)}: {os.path.basename(image_path)}")
            
            try:
                # OCR识别
                ocr_result = self.ocr_processor.process_image(image_path)
                if not ocr_result.success:
                    print(f"    ❌ OCR失败: {ocr_result.error_message}")
                    performance_data["failed_analyses"] += 1
                    continue
                
                chinese_regions = ocr_result.data.chinese_regions
                if len(chinese_regions) < 2:
                    print(f"    ⚠️ 文字区域不足(<2)，跳过样式分组")
                    continue
                
                # 样式分组分析 - 使用自定义算法或默认算法
                if custom_grouping_func:
                    try:
                        # 准备数据给Gemini优化的算法
                        text_data = []
                        for region in chinese_regions:
                            text_data.append({
                                'text': region.text,
                                'precise_height': region.style_info.get('precise_height', 0) if region.style_info else 0,
                                'text_color': region.style_info.get('text_color', (0, 0, 0)) if region.style_info else (0, 0, 0),
                                'bbox': region.bbox
                            })
                        
                        # 调用Gemini优化的算法
                        optimized_result = custom_grouping_func(text_data)
                        
                        # 转换结果格式以兼容现有代码
                        style_groups = []
                        for group_data in optimized_result:
                            group_regions = []
                            # 处理不同的返回格式
                            if isinstance(group_data, dict) and 'texts' in group_data:
                                # 格式1: {'texts': [...], 'avg_height': ..., ...}
                                texts_in_group = group_data['texts']
                            elif isinstance(group_data, list) and all(isinstance(item, dict) for item in group_data):
                                # 格式2: [{'text': '...', 'precise_height': ...}, ...]
                                texts_in_group = [item['text'] for item in group_data]
                            else:
                                # 其他格式，跳过
                                continue
                                
                            # 找到对应的region对象
                            for text in texts_in_group:
                                for region in chinese_regions:
                                    if region.text == text:
                                        group_regions.append(region)
                                        break
                            if group_regions:
                                style_groups.append(group_regions)
                        
                        print(f"    ✅ 使用Gemini优化算法: {len(chinese_regions)}个文字区域, {len(style_groups)}个样式组")
                        
                    except Exception as e:
                        print(f"    ❌ Gemini算法执行失败: {e}, 回退使用默认算法")
                        # 回退到默认算法
                        if algorithm_type == "strict":
                            style_groups = self.strict_style_grouping(chinese_regions)
                        else:
                            style_groups = self.tolerance_style_grouping(chinese_regions)
                else:
                    # 使用默认算法
                    if algorithm_type == "strict":
                        style_groups = self.strict_style_grouping(chinese_regions)
                    elif algorithm_type == "tolerance":
                        style_groups = self.tolerance_style_grouping(chinese_regions)
                    else:
                        print(f"    ❌ 未知的算法类型: {algorithm_type}")
                        performance_data["failed_analyses"] += 1
                        continue
                    
                    print(f"    ✅ 使用默认算法: {len(chinese_regions)}个文字区域, {len(style_groups)}个样式组")

                # 记录分析结果
                image_result = {
                    "image_path": image_path,
                    "image_name": os.path.basename(image_path),
                    "text_regions_count": len(chinese_regions),
                    "total_groups": len(style_groups),
                    "single_text_groups": len([g for g in style_groups if len(g) == 1]),
                    "multi_text_groups": len([g for g in style_groups if len(g) > 1]),
                    "max_group_size": max(len(g) for g in style_groups) if style_groups else 0,
                    "style_groups": [
                        {
                            "group_id": i,
                            "texts": [region.text for region in group],
                            "size": len(group),
                            "height_variance": self.calculate_height_variance(group),
                            "color_variance": self.calculate_color_variance(group),
                            "representative_style": {
                                "height": group[0].style_info.get('precise_height', 0) if group and group[0].style_info else 0,
                                "color": group[0].style_info.get('text_color', (0, 0, 0)) if group and group[0].style_info else (0, 0, 0)
                            }
                        } for i, group in enumerate(style_groups, 1)
                    ],
                    "text_regions": [
                        {
                            "text": region.text,
                            "bbox": region.bbox,
                            "confidence": region.score,
                            "precise_height": region.style_info.get('precise_height', 0) if region.style_info else 0,
                            "text_color": region.style_info.get('text_color', (0, 0, 0)) if region.style_info else (0, 0, 0)
                        } for region in chinese_regions
                    ]
                }
                
                performance_data["image_results"].append(image_result)
                performance_data["successful_analyses"] += 1
                
                # 更新统计信息
                stats = performance_data["grouping_statistics"]
                stats["total_groups"] += len(style_groups)
                stats["single_text_groups"] += len([g for g in style_groups if len(g) == 1])
                stats["multi_text_groups"] += len([g for g in style_groups if len(g) > 1])
                stats["max_group_size"] = max(stats["max_group_size"], max(len(g) for g in style_groups) if style_groups else 0)
                
                print(f"    ✅ 成功: {len(chinese_regions)}个文字区域, {len(style_groups)}个样式组")
                
                # 生成可视化图片（在调试目录）
                debug_dir = "style_grouping_debug"
                os.makedirs(debug_dir, exist_ok=True)
                self.visualize_style_groups(image_path, style_groups, debug_dir)
                
            except Exception as e:
                print(f"    ❌ 异常: {e}")
                performance_data["failed_analyses"] += 1
        
        # 计算平均组大小
        if performance_data["grouping_statistics"]["total_groups"] > 0:
            total_texts = sum(result["text_regions_count"] for result in performance_data["image_results"])
            performance_data["grouping_statistics"]["average_group_size"] = total_texts / performance_data["grouping_statistics"]["total_groups"]
        
        # 分析样式分组模式
        self._analyze_grouping_patterns(performance_data)
        
        return performance_data
    
    def _analyze_grouping_patterns(self, performance_data: Dict[str, Any]) -> None:
        """分析样式分组模式"""
        image_results = performance_data["image_results"]
        
        if not image_results:
            return
        
        # 统计分组数量分布
        group_counts = [result["total_groups"] for result in image_results]
        avg_groups = sum(group_counts) / len(group_counts) if group_counts else 0
        
        # 统计单文字组比例
        total_single = sum(result["single_text_groups"] for result in image_results)
        total_multi = sum(result["multi_text_groups"] for result in image_results)
        single_ratio = total_single / (total_single + total_multi) if (total_single + total_multi) > 0 else 0
        
        # 分析文字区域数量分布
        region_counts = [result["text_regions_count"] for result in image_results]
        avg_regions = sum(region_counts) / len(region_counts) if region_counts else 0
        
        # 分析组大小分布
        all_group_sizes = []
        for result in image_results:
            for group in result["style_groups"]:
                all_group_sizes.append(group["size"])
        
        performance_data["statistics"] = {
            "average_groups_per_image": avg_groups,
            "average_text_regions": avg_regions,
            "min_text_regions": min(region_counts) if region_counts else 0,
            "max_text_regions": max(region_counts) if region_counts else 0,
            "single_text_group_ratio": single_ratio,
            "group_size_distribution": {
                "min": min(all_group_sizes) if all_group_sizes else 0,
                "max": max(all_group_sizes) if all_group_sizes else 0,
                "average": sum(all_group_sizes) / len(all_group_sizes) if all_group_sizes else 0
            }
        }
        
        print(f"\n📈 样式分组统计:")
        print(f"  成功率: {performance_data['successful_analyses']}/{performance_data['total_images']} ({performance_data['successful_analyses']/performance_data['total_images']*100:.1f}%)")
        print(f"  平均文字区域数: {avg_regions:.1f}")
        print(f"  平均分组数: {avg_groups:.1f}")
        print(f"  单文字组比例: {single_ratio*100:.1f}%")
        print(f"  组大小分布: 最小{min(all_group_sizes) if all_group_sizes else 0}, 最大{max(all_group_sizes) if all_group_sizes else 0}, 平均{sum(all_group_sizes) / len(all_group_sizes) if all_group_sizes else 0:.1f}")
    
    def create_optimization_prompt(self, performance_data: Dict[str, Any], 
                                 current_algorithm: str = None, 
                                 iteration_number: int = 1,
                                 user_corrected_groupings: Dict[str, Any] = None) -> str:
        """创建样式分组算法优化提示词"""
        
        # 构建图片数据摘要
        images_summary = []
        for result in performance_data["image_results"]:
            # 构建当前程序的分组情况
            current_group_info = []
            for group in result["style_groups"]:
                texts = ", ".join([f'"{text}"' for text in group["texts"]])
                style = group["representative_style"]
                group_info = f"组{group['group_id']}: [{texts}] (高度:{style['height']}px, 颜色:{style['color']}, 大小:{group['size']})"
                current_group_info.append(group_info)
            
            # 构建用户修正后的正确分组（如果有）
            corrected_group_info = ""
            if user_corrected_groupings and result['image_name'] in user_corrected_groupings:
                corrected_groups = user_corrected_groupings[result['image_name']]
                corrected_info = []
                for group_name, texts in corrected_groups.items():
                    texts_str = ", ".join([f'"{text}"' for text in texts])
                    corrected_info.append(f"{group_name}: [{texts_str}]")
                corrected_group_info = f"""
- **用户修正后的正确分组**:
  {chr(10).join(f"  {info}" for info in corrected_info)}"""

            image_info = f"""
图片: {result['image_name']}
- 文字区域: {result['text_regions_count']}个
- **程序当前分组结果**: {result['total_groups']}个组 (单文字组:{result['single_text_groups']}, 多文字组:{result['multi_text_groups']})
- **程序实际分组详情**:
  {chr(10).join(f"  {info}" for info in current_group_info)}{corrected_group_info}
- **原始文字数据**:
  {chr(10).join(f'  "{region["text"]}" - 高度:{region["precise_height"]}px, 颜色:{region["text_color"]}' for region in result["text_regions"])}
"""
            images_summary.append(image_info)
        
        images_data = "\n".join(images_summary)
        
        # 构建当前算法性能摘要
        stats = performance_data.get("statistics", {})
        performance_summary = f"""
当前样式分组算法性能分析:
- 算法类型: {performance_data.get('algorithm_type', 'tolerance')}
- 成功率: {performance_data['successful_analyses']}/{performance_data['total_images']} ({performance_data['successful_analyses']/performance_data['total_images']*100:.1f}%)
- 平均文字区域数: {stats.get('average_text_regions', 0):.1f}
- 平均分组数: {stats.get('average_groups_per_image', 0):.1f}
- 单文字组比例: {stats.get('single_text_group_ratio', 0)*100:.1f}%
- 组大小分布: {stats.get('group_size_distribution', {})}
"""
        
        # 当前算法代码（如果有）
        current_algo_section = ""
        if current_algorithm:
            current_algo_section = f"""
当前算法实现:
```python
{current_algorithm}
```
"""

        # 问题分析指导
        problem_analysis_guide = ""
        if user_corrected_groupings:
            problem_analysis_guide = f"""
## 重要说明
用户已经提供了每张图片的**正确分组结果**，这是经过人工分析得出的标准答案。请你：

1. **对比分析**: 仔细比较"程序当前分组结果"与"用户修正后的正确分组"的差异
2. **识别问题**: 指出程序分组过细、过粗或分组错误的具体情况
3. **分析原因**: 从高度阈值、颜色阈值等参数角度分析导致错误分组的原因
4. **优化策略**: 基于这些具体问题提出针对性的算法改进方案
"""

        prompt = f"""你是一个专业的文字样式分组算法专家。请分析以下图片数据和当前算法性能，优化样式分组算法。

样式分组规则说明:
- 相同字号（精确像素高度相近） + 相同颜色（RGB相近）= 同一组
- 任何一个维度不同 = 不同组  
- 目标是视觉上看起来统一的文字归为一组
- 宁可分组过细，不要分组过粗

{problem_analysis_guide}

{performance_summary}

图片分析数据:
{images_data}

{current_algo_section}

请你完成以下任务:

1. **程序分组问题诊断**: 
   对每张图片，按以下格式逐一分析：
   ```
   **X.jpg/png**: 
   - 程序当前识别结果: [详细列出程序实际分组情况，包括组数和每组内容]
   - 问题诊断: [指出分组过细/过粗/错误归类的具体问题] 
   - 正确分组应该是: [基于用户提供的修正分组，说明正确的分组方式]
   - 数据分析: [从高度差异、颜色差异、阈值问题等角度分析错误原因]
   ```

2. **算法参数问题分析**: 
   - 当前高度容忍度是否合适
   - 当前颜色容忍度是否合适
   - 是否需要其他维度的相似度计算

3. **优化策略设计**:
   - 针对发现的具体问题提出解决方案
   - 参数调优建议（高度阈值、颜色阈值等）
   - 算法逻辑改进点

4. **代码实现**: 提供完整的Python算法实现

请按以下格式返回:

## 程序分组问题诊断
[按上述格式逐张图片分析程序分组错误，对比正确分组，指出具体问题]

## 算法参数问题分析
[分析当前阈值参数的不足，从数据角度找出问题根源]

## 优化策略
[详细说明优化思路和改进点，包括参数调优建议]

## 优化后的算法实现
```python
# 在这里提供完整的优化算法代码
# 包括样式分组函数、相似度计算、阈值优化等
# 可使用的参数：precise_height（像素级高度）、text_color（RGB颜色）、bbox（OCR文本框）
# 如果需要更高级算法，可考虑使用 sklearn.cluster.DBSCAN
```

## 预期改进效果
[说明优化后预期的分组准确率提升和改进点]

注意:
- 这是第{iteration_number}次迭代优化
- 请重点关注程序当前分组与正确分组的具体差异
- 对每张图片都要详细分析程序识别结果和问题所在
- 必须按照指定格式逐一分析每张图片
- 算法简单实用，参数可调节
- 只使用 precise_height、text_color、bbox 等基础特征
- 重点关注视觉一致性而非复杂计算
- 算法应该具有良好的容错性
"""
        
        return prompt
    
    def call_gemini_for_optimization(self, prompt: str, image_files: List[str]) -> Dict[str, Any]:
        """调用Gemini进行算法优化"""
        print(f"\n🤖 正在请求{self.model}进行算法优化...")
        print(f"📸 附带{len(image_files)}张参考图片")
        
        # 构建请求内容
        content_parts = [{"text": prompt}]
        
        # 添加图片
        for image_path in image_files:
            try:
                base64_image = self.encode_image_to_base64(image_path)
                image_part = {
                    "inline_data": {
                        "mime_type": "image/jpeg" if image_path.lower().endswith('.jpg') else "image/png",
                        "data": base64_image
                    }
                }
                content_parts.append(image_part)
                print(f"  ✅ 添加图片: {os.path.basename(image_path)}")
            except Exception as e:
                print(f"  ❌ 图片编码失败: {os.path.basename(image_path)} - {e}")
        
        # 构建请求
        headers = {"Content-Type": "application/json"}
        url_with_key = f"{self.api_url}?key={self.api_key}"
        
        payload = {
            "contents": [{"parts": content_parts}],
            "generationConfig": {
                "maxOutputTokens": 32000,  # 增加token限制以支持长代码
                "temperature": 0.1
            }
        }
        
        try:
            response = requests.post(url_with_key, headers=headers, json=payload, timeout=180)
            response.raise_for_status()
            
            result = response.json()
            
            if 'candidates' in result and len(result['candidates']) > 0:
                content = result['candidates'][0]['content']['parts'][0]['text']
                
                # 打印token使用情况
                usage = result.get('usageMetadata', {})
                print(f"✅ {self.model}响应成功")
                print(f"📊 Token使用情况:")
                print(f"  输入Token: {usage.get('promptTokenCount', '未知')}")
                print(f"  输出Token: {usage.get('candidatesTokenCount', '未知')}")
                print(f"  总Token: {usage.get('totalTokenCount', '未知')}")
                
                return {
                    "success": True,
                    "content": content,
                    "token_usage": usage
                }
            else:
                return {
                    "success": False,
                    "error": "无效的响应格式"
                }
                
        except requests.RequestException as e:
            return {
                "success": False,
                "error": f"API请求失败: {e}"
            }
    
    def extract_algorithm_code(self, gemini_response: str) -> Optional[str]:
        """从Gemini响应中提取算法代码"""
        import re
        
        # 查找Python代码块
        code_pattern = r'```python\s*\n(.*?)\n```'
        matches = re.findall(code_pattern, gemini_response, re.DOTALL)
        
        if matches:
            # 返回最长的代码块（通常是主要实现）
            longest_code = max(matches, key=len)
            return longest_code.strip()
        
        return None
    
    def save_iteration_results(self, iteration_number: int, 
                             performance_data: Dict[str, Any],
                             gemini_response: Dict[str, Any],
                             extracted_code: Optional[str]) -> str:
        """保存迭代结果"""
        iteration_dir = os.path.join(self.output_dir, f"iteration_{iteration_number}")
        os.makedirs(iteration_dir, exist_ok=True)
        
        # 保存性能数据
        performance_file = os.path.join(iteration_dir, "performance_analysis.json")
        with open(performance_file, 'w', encoding='utf-8') as f:
            json.dump(performance_data, f, ensure_ascii=False, indent=2)
        
        # 保存Gemini完整响应
        response_file = os.path.join(iteration_dir, "gemini_response.md")
        with open(response_file, 'w', encoding='utf-8') as f:
            if gemini_response["success"]:
                f.write(gemini_response["content"])
            else:
                f.write(f"❌ 请求失败: {gemini_response['error']}")
        
        # 保存提取的算法代码
        if extracted_code:
            code_file = os.path.join(iteration_dir, "optimized_algorithm.py")
            with open(code_file, 'w', encoding='utf-8') as f:
                f.write(f'''"""
第{iteration_number}次迭代优化的布局识别算法
生成时间: {datetime.now().isoformat()}
"""

{extracted_code}
''')
        
        # 保存迭代摘要
        summary = {
            "iteration_number": iteration_number,
            "timestamp": datetime.now().isoformat(),
            "performance_summary": {
                "success_rate": f"{performance_data['successful_analyses']}/{performance_data['total_images']}",
                "success_percentage": performance_data['successful_analyses']/performance_data['total_images']*100 if performance_data['total_images'] > 0 else 0
            },
            "has_extracted_code": extracted_code is not None,
            "token_usage": gemini_response.get("token_usage", {}),
            "gemini_success": gemini_response["success"]
        }
        
        summary_file = os.path.join(iteration_dir, "iteration_summary.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        self.iteration_history.append(summary)
        
        print(f"💾 第{iteration_number}次迭代结果已保存到: {iteration_dir}")
        
        return iteration_dir
    
    def run_optimization_iteration(self, iteration_number: int = 1, 
                                 current_algorithm: str = None,
                                 algorithm_type: str = "tolerance",
                                 user_corrected_groupings: Dict[str, Any] = None) -> Dict[str, Any]:
        """运行一次样式分组算法优化迭代"""
        print(f"\n🔄 开始第{iteration_number}次样式分组算法优化迭代")
        print(f"{'='*60}")
        
        # 1. 获取图片文件
        image_files = self.get_image_files()
        if not image_files:
            return {
                "success": False,
                "error": f"在 {self.images_dir} 目录中未找到图片文件"
            }
        
        print(f"📁 找到 {len(image_files)} 张图片")
        
        # 2. 分析当前样式分组算法性能
        performance_data = self.analyze_current_grouping_performance(image_files, algorithm_type, current_algorithm)
        
        # 3. 创建优化提示词
        prompt = self.create_optimization_prompt(performance_data, current_algorithm, iteration_number, user_corrected_groupings)
        
        # 4. 调用Gemini进行优化
        gemini_response = self.call_gemini_for_optimization(prompt, image_files)
        
        if not gemini_response["success"]:
            return {
                "success": False,
                "error": f"Gemini调用失败: {gemini_response['error']}"
            }
        
        # 5. 提取算法代码
        extracted_code = self.extract_algorithm_code(gemini_response["content"])
        
        # 6. 保存迭代结果
        iteration_dir = self.save_iteration_results(
            iteration_number, performance_data, gemini_response, extracted_code
        )
        
        # 7. 返回结果摘要
        return {
            "success": True,
            "iteration_number": iteration_number,
            "iteration_dir": iteration_dir,
            "performance_data": performance_data,
            "extracted_code": extracted_code,
            "has_code": extracted_code is not None,
            "gemini_response_length": len(gemini_response["content"]),
            "token_usage": gemini_response.get("token_usage", {})
        }
    
    def run_multi_iteration_optimization(self, max_iterations: int = 3, 
                                        user_corrected_groupings: Dict[str, Any] = None) -> Dict[str, Any]:
        """运行多轮迭代优化 - 每轮需要用户确认"""
        print(f"\n🚀 开始多轮样式分组算法优化 - 最多{max_iterations}轮迭代")
        print(f"📋 每轮完成后将询问您是否继续下一轮")
        if user_corrected_groupings:
            print(f"📋 已加载用户修正的分组数据，将作为优化参考标准")
        print(f"{'='*80}")
        
        results = []
        current_algorithm = None
        
        for iteration in range(1, max_iterations + 1):
            print(f"\n📍 准备进行第{iteration}/{max_iterations}轮迭代")
            
            # 如果不是第一轮，询问用户是否继续
            if iteration > 1:
                self._print_iteration_summary(results[-1])  # 显示上一轮结果摘要
                
                while True:
                    user_input = input(f"\n❓ 是否继续第{iteration}轮优化？(y/yes/是 继续, n/no/否 停止, s/skip/跳过 查看结果): ").strip().lower()
                    
                    if user_input in ['n', 'no', '否', 'stop', '停止']:
                        print(f"🛑 用户选择停止，共完成{iteration-1}轮迭代")
                        break
                    elif user_input in ['s', 'skip', '跳过', 'show', '查看']:
                        print(f"📊 查看第{iteration-1}轮详细结果...")
                        self._show_detailed_results(results[-1])
                        continue
                    elif user_input in ['y', 'yes', '是', 'continue', '继续', '']:
                        print(f"✅ 继续第{iteration}轮优化")
                        break
                    else:
                        print("❌ 无效输入，请输入 y/yes/是 (继续) 或 n/no/否 (停止) 或 s/skip/跳过 (查看结果)")
                
                # 如果用户选择停止，跳出循环
                if user_input in ['n', 'no', '否', 'stop', '停止']:
                    break
            
            # 运行单次迭代
            result = self.run_optimization_iteration(iteration, current_algorithm, "tolerance", user_corrected_groupings)
            results.append(result)
            
            if not result["success"]:
                print(f"❌ 第{iteration}轮迭代失败: {result['error']}")
                print(f"🤔 是否尝试手动修复后继续？")
                break
            
            # 如果提取到了代码，作为下一轮的输入
            if result["has_code"]:
                current_algorithm = result["extracted_code"]
                print(f"✅ 第{iteration}轮完成，提取到算法代码")
                print(f"📏 代码长度: {len(current_algorithm)} 字符")
            else:
                print(f"⚠️ 第{iteration}轮完成，但未能提取到算法代码")
                print(f"🔍 请检查 Gemini 回答格式是否正确")
            
            # 如果是最后一轮，直接结束
            if iteration == max_iterations:
                print(f"🏁 已完成最大迭代轮数 {max_iterations}")
                break
        
        # 生成总结报告
        self._generate_final_report(results)
        
        return {
            "success": True,
            "total_iterations": len(results),
            "successful_iterations": sum(1 for r in results if r["success"]),
            "results": results,
            "final_algorithm": current_algorithm
        }
    
    def run_auto_optimization(self, max_iterations: int = 3, 
                            user_corrected_groupings: Dict[str, Any] = None) -> Dict[str, Any]:
        """运行自动模式的多轮迭代优化 - 不需要用户确认"""
        print(f"\n🤖 开始自动模式样式分组算法优化 - {max_iterations}轮迭代")
        print(f"⚠️ 自动模式将连续运行，不会询问用户确认")
        if user_corrected_groupings:
            print(f"📋 已加载用户修正的分组数据，将作为优化参考标准")
        print(f"{'='*80}")
        
        results = []
        current_algorithm = None
        
        for iteration in range(1, max_iterations + 1):
            print(f"\n📍 第{iteration}/{max_iterations}轮迭代")
            
            # 运行单次迭代
            result = self.run_optimization_iteration(iteration, current_algorithm, "tolerance", user_corrected_groupings)
            results.append(result)
            
            if not result["success"]:
                print(f"❌ 第{iteration}轮迭代失败: {result['error']}")
                break
            
            # 如果提取到了代码，作为下一轮的输入
            if result["has_code"]:
                current_algorithm = result["extracted_code"]
                print(f"✅ 第{iteration}轮完成，提取到算法代码，将用于下一轮优化")
            else:
                print(f"⚠️ 第{iteration}轮完成，但未能提取到算法代码")
            
            # 自动模式的间隔（除了最后一轮）
            if iteration < max_iterations:
                print(f"⏱️ 等待10秒后开始下一轮...")
                import time
                time.sleep(10)
        
        # 生成总结报告
        self._generate_final_report(results)
        
        return {
            "success": True,
            "total_iterations": len(results),
            "successful_iterations": sum(1 for r in results if r["success"]),
            "results": results,
            "final_algorithm": current_algorithm
        }
    
    def _print_iteration_summary(self, result: Dict[str, Any]) -> None:
        """打印迭代结果摘要"""
        if not result["success"]:
            print(f"❌ 上一轮迭代失败: {result['error']}")
            return
        
        perf = result["performance_data"]
        success_rate = perf['successful_analyses']/perf['total_images']*100 if perf['total_images'] > 0 else 0
        
        print(f"\n📊 第{result['iteration_number']}轮迭代结果摘要:")
        print(f"  ✅ 算法成功率: {perf['successful_analyses']}/{perf['total_images']} ({success_rate:.1f}%)")
        print(f"  🧠 提取到代码: {'是' if result['has_code'] else '否'}")
        print(f"  📄 Gemini回答长度: {result['gemini_response_length']} 字符")
        
        # 显示统计信息
        stats = perf.get("statistics", {})
        if stats:
            print(f"  📈 布局模式分布: {stats.get('layout_mode_distribution', {})}")
            print(f"  🎯 对齐类型分布: {stats.get('alignment_type_distribution', {})}")
        
        print(f"  📁 详细结果保存在: {result['iteration_dir']}")
    
    def _show_detailed_results(self, result: Dict[str, Any]) -> None:
        """显示详细的迭代结果"""
        if not result["success"]:
            print(f"❌ 该轮迭代失败，无详细结果可显示")
            return
        
        print(f"\n" + "="*60)
        print(f"📋 第{result['iteration_number']}轮迭代详细结果")
        print(f"="*60)
        
        # 性能详情
        perf = result["performance_data"]
        print(f"\n🎯 性能分析:")
        for img_result in perf["image_results"]:
            alignment_info = img_result["alignment_groups"]
            total_groups = sum(alignment_info.values())
            print(f"  📸 {img_result['image_name']}: {img_result['text_regions_count']}个文字, {total_groups}个分组")
            print(f"     布局: {img_result['layout_mode']}, 主对齐: {img_result['main_alignment']}")
        
        # 代码预览
        if result["has_code"]:
            code = result["extracted_code"]
            lines = code.split('\n')
            preview_lines = min(10, len(lines))
            print(f"\n💻 算法代码预览 (前{preview_lines}行):")
            for i, line in enumerate(lines[:preview_lines], 1):
                print(f"  {i:2d}| {line}")
            if len(lines) > preview_lines:
                print(f"     ... (还有{len(lines)-preview_lines}行)")
        
        # 文件路径
        print(f"\n📁 完整结果文件:")
        iteration_dir = result['iteration_dir']
        files = [
            "performance_analysis.json",
            "gemini_response.md", 
            "optimized_algorithm.py" if result["has_code"] else None,
            "iteration_summary.json"
        ]
        for file in files:
            if file:
                print(f"  📄 {os.path.join(iteration_dir, file)}")
        
        print(f"="*60)
    
    def _generate_final_report(self, results: List[Dict[str, Any]]) -> None:
        """生成最终优化报告"""
        report_file = os.path.join(self.output_dir, "optimization_final_report.md")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"# Gemini算法优化最终报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write(f"## 优化概览\n")
            f.write(f"- 总迭代轮数: {len(results)}\n")
            f.write(f"- 成功轮数: {sum(1 for r in results if r['success'])}\n")
            f.write(f"- 图片样本数: {len(self.get_image_files())}\n\n")
            
            f.write(f"## 各轮迭代详情\n\n")
            for i, result in enumerate(results, 1):
                if result["success"]:
                    perf = result["performance_data"]
                    success_rate = perf['successful_analyses']/perf['total_images']*100 if perf['total_images'] > 0 else 0
                    
                    f.write(f"### 第{i}轮迭代\n")
                    f.write(f"- 算法成功率: {perf['successful_analyses']}/{perf['total_images']} ({success_rate:.1f}%)\n")
                    f.write(f"- 提取到代码: {'是' if result['has_code'] else '否'}\n")
                    f.write(f"- Token使用: {result.get('token_usage', {}).get('totalTokenCount', '未知')}\n")
                    f.write(f"- 结果目录: `{result['iteration_dir']}`\n\n")
                else:
                    f.write(f"### 第{i}轮迭代\n")
                    f.write(f"- 状态: ❌ 失败\n")
                    f.write(f"- 错误: {result['error']}\n\n")
            
            f.write(f"## 图片样本列表\n\n")
            for img_file in self.get_image_files():
                f.write(f"- {os.path.basename(img_file)}\n")
        
        print(f"📊 最终优化报告已生成: {report_file}")

    def parse_user_corrected_groupings(self) -> Dict[str, Any]:
        """解析用户提供的修正分组数据"""
        user_corrections = {
            "1.jpg": {
                "组1 (大标题)": ["4D高回弹记忆棉", "久睡不塌", "适用更久"],
                "组2 (中标题)": ["\"0压感\"", "高回弹海绵"],
                "组3 (小正文)": ["深度分散压力", "不易塌陷"]
            },
            "2.jpg": {
                "组1 (注释)": ["*产品尺寸为手工测量，以实际产品为准"],
                "组2 (标签)": ["产品名称", "产品材质", "尺寸", "颜色"],
                "组3 (内容)": ["吸盘兔型湿厕巾收纳架", "卷纸款：15.3*6*24cm", "不锈钢+ABS", "胡桃木色", "湿巾款：15.3*12.6*11cm"]
            },
            "3.jpg": {
                "组1 (标题)": ["店铺重要通知"],
                "组2 (正文)": ["本店铺所有产品价格均为出厂价格...", "产品详情及价格仅供参考...", "请联系客服重新加税点报价..."],
                "组3 (按钮)": ["确定收到"]
            },
            "4.png": {
                "组1 (白底黑字)": ["加粗钢管", "约15mm"],
                "组2 (蓝底白字)": ["加粗升级款", "常规薄款"],
                "组3 (灰色说明文字)": ["约15*15mm加粗管架", "稳稳承托桌面不易摇晃", "约10*10mm薄壁管架", "框架薄易摇晃"]
            },
            "5.jpg": {
                "组1 (标题)": ["花样收纳", "想吸哪里吸哪里"],
                "组2 (标签)": ["电视遥控", "马桶遥控", "空调遥控", "窗帘遥控"]
            },
            "6.jpg": {
                "组1 (大标题)": ["控油祛痘", "肌肤焕新"],
                "组2 (副标题)": ["tondl水杨酸祛痘舒护棉片"],
                "组3 (圆圈内白字)": ["专利", "舒缓"],
                "组4 (底部白字)": ["净肤控油", "肌肤细嫩透"],
                "组5 (红色小字)": ["1%盒引", "水杨酸", "微肤"],
                "组6 (棕色小字)": ["平衡水油", "清洁毛孔"],
                "组7 (其他单独成组)": ["韩国进口品牌", "维稳安肤", "控油", "舒级", "净润控油"]
            },
            "7.jpg": {
                "组1 (大标题)": ["源自核心产区茶园"],
                "组2 (标题)": ["选自高山野长茶树"],
                "组3 (副标题)": ["每一颗高品质的茶都是我们的初心"],
                "组4 (正文段落)": ["成长于白茶黄金生长区...", "日照适度，且多为柔和漫射光...", "累，内含物质更丰富..."]
            },
            "8.jpg": {
                "组1": ["洗脸盆", "洗衣盆", "婴儿盆"]
            },
            "9.png": {
                "组1 (顶部标签)": ["我们用心，用户才放心"],
                "组2 (中标题)": ["小口袋", "大口袋"],
                "组3 (说明文字)": ["可放置水杯、饮料", "或一些小物品", "可以放一些零食", "饮料等也可以做", "垃圾桶哦"]
            },
            "10.png": {
                "组1 (顶部标签)": ["可背也可提", "轻量便捷"],
                "组2 (内容)": ["轻轻松松一提就走"]
            }
        }
        
        print(f"📋 已加载用户修正的分组数据，覆盖 {len(user_corrections)} 张图片")
        for image_name, groups in user_corrections.items():
            total_texts = sum(len(texts) for texts in groups.values())
            print(f"  📸 {image_name}: {len(groups)}个组, {total_texts}个文字")
        
        return user_corrections

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Gemini样式分组算法优化器")
    parser.add_argument("--iterations", "-i", type=int, default=8, help="最大迭代次数")
    parser.add_argument("--single", "-s", action="store_true", help="只运行单次迭代")
    parser.add_argument("--interactive", "-I", action="store_true", help="交互模式，每轮询问是否继续(默认)")
    parser.add_argument("--auto", "-a", action="store_true", help="自动模式，不询问直接运行所有轮次")
    parser.add_argument("--use-corrected-groupings", "-c", action="store_true", help="使用用户修正的分组数据作为优化参考")
    
    args = parser.parse_args()
    
    # 创建优化器
    optimizer = GeminiStyleGroupingOptimizer()
    
    # 获取用户修正的分组数据（如果启用）
    user_corrected_groupings = None
    if args.use_corrected_groupings:
        user_corrected_groupings = optimizer.parse_user_corrected_groupings()
    
    if args.single:
        # 单次迭代
        result = optimizer.run_optimization_iteration(1, None, "tolerance", user_corrected_groupings)
        if result["success"]:
            print(f"\n🎉 单次优化完成！")
            print(f"📁 结果保存在: {result['iteration_dir']}")
            if result["has_code"]:
                print(f"✅ 成功提取算法代码")
                print(f"💡 可以查看文件: {os.path.join(result['iteration_dir'], 'optimized_algorithm.py')}")
            else:
                print(f"⚠️ 未能提取算法代码")
                print(f"📄 请查看Gemini回答: {os.path.join(result['iteration_dir'], 'gemini_response.md')}")
        else:
            print(f"\n❌ 优化失败: {result['error']}")
    else:
        # 选择运行模式
        if args.auto:
            # 自动模式 - 添加原来的自动运行逻辑
            result = optimizer.run_auto_optimization(args.iterations, user_corrected_groupings)
        else:
            # 交互模式（默认）
            result = optimizer.run_multi_iteration_optimization(args.iterations, user_corrected_groupings)
        
        if result["success"]:
            print(f"\n🎉 多轮优化完成！")
            print(f"📊 总计 {result['successful_iterations']}/{result['total_iterations']} 轮成功")
            print(f"📁 所有结果保存在: {optimizer.output_dir}")
            print(f"📋 查看最终报告: {os.path.join(optimizer.output_dir, 'optimization_final_report.md')}")
        else:
            print(f"\n❌ 多轮优化过程中出现问题")
    
    # 清理资源
    optimizer.cleanup()

if __name__ == "__main__":
    main()