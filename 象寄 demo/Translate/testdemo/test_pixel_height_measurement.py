"""
像素级高度测量独立测试程序
专注于文字区域的像素级高度测量算法测试和调试

核心功能：
1. OCR文字识别
2. 像素级高度测量（OTSU + 轮廓检测）
3. 调试图片生成：
   - OTSU二值化处理图
   - 轮廓检测图  
   - 原图标注图（透明图层标注OCR范围+像素级高度）

特点：
- 只测量未统一的原始像素级高度
- 不进行高度统一处理
- 不进行字号搜索匹配
- 专注于高度测量算法本身

作者：前辈指导开发
"""

import os
import cv2
import numpy as np
import json
from typing import List, Tuple, Dict, Any, Optional
from PIL import Image, ImageDraw, ImageFont
from paddleocr import PaddleOCR
import re


def safe_resize(image, max_side=1500):
    h, w = image.shape[:2]
    if max(h, w) > max_side:
        scale = max_side / max(h, w)
        image = cv2.resize(image, (int(w*scale), int(h*scale)), interpolation=cv2.INTER_AREA)
    return image


class PixelHeightMeasurementTester:
    """像素级高度测量测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.ocr = None
        self.debug_dir = "pixel_height_debug"
        self.ensure_debug_dir()
        
        # 初始化中文字体
        self.init_chinese_font()
        
        print("🔍 像素级高度测量测试器初始化完成")
    
    def ensure_debug_dir(self):
        """确保调试目录存在"""
        os.makedirs(self.debug_dir, exist_ok=True)
        print(f"📁 调试目录: {self.debug_dir}")
    
    def init_chinese_font(self):
        """初始化中文字体"""
        try:
            # 尝试多个可能的中文字体路径
            font_paths = [
                "../fonts/思源黑体/SourceHanSans-Regular.ttf",
                "../fonts/NotoSansSC/NotoSansSC-Black.ttf", 
                "../fonts/台北黑体/TaipeiSans-Bold.ttf",
                "/System/Library/Fonts/PingFang.ttc",  # macOS系统字体
                "/System/Library/Fonts/Hiragino Sans GB.ttc",  # macOS系统字体
            ]
            
            self.chinese_font = None
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        self.chinese_font = ImageFont.truetype(font_path, 16)
                        print(f"✅ 加载中文字体: {os.path.basename(font_path)}")
                        break
                    except Exception as e:
                        continue
            
            if self.chinese_font is None:
                print("⚠️ 未找到中文字体，使用默认字体")
                self.chinese_font = ImageFont.load_default()
                
        except Exception as e:
            print(f"⚠️ 字体初始化失败: {e}")
            self.chinese_font = ImageFont.load_default()
    
    def get_ocr_instance(self):
        """获取OCR实例"""
        if self.ocr is None:
            print("🔍 初始化PaddleOCR...")
            self.ocr = PaddleOCR(
                use_doc_orientation_classify=False,
                use_doc_unwarping=False,
                use_textline_orientation=False
            )
            print("✅ PaddleOCR初始化完成")
        return self.ocr
    
    def is_chinese_text(self, text: str) -> bool:
        """判断是否为中文文本"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))
    
    def extract_text_regions(self, image_path: str) -> List[Dict]:
        """提取文字区域"""
        print(f"\n🔍 开始OCR识别: {image_path}")
        
        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法加载图像: {image_path}")
        
        # OCR识别
        ocr = self.get_ocr_instance()
        result = ocr.predict(input=image_path)
        
        if not result:
            print("❌ 未检测到文字")
            return []
        
        # 解析OCR结果
        regions = []
        for res in result:
            if 'rec_texts' in res and 'dt_polys' in res:
                for i, (poly, text, score) in enumerate(zip(
                    res['dt_polys'], 
                    res['rec_texts'], 
                    res.get('rec_scores', [1.0] * len(res['rec_texts']))
                )):
                    if score < 0.5:  # 置信度阈值
                        continue
                    
                    # 计算边界框
                    points = np.array(poly, dtype=np.int32)
                    x, y, w, h = cv2.boundingRect(points)
                    
                    # 判断是否为中文
                    is_chinese = self.is_chinese_text(text)
                    
                    region = {
                        'id': len(regions),
                        'text': text,
                        'bbox': (x, y, w, h),
                        'poly': poly,
                        'score': score,
                        'is_chinese': is_chinese,
                        'center': (x + w//2, y + h//2),
                        'ocr_height': h  # 原始OCR高度
                    }
                    regions.append(region)
        
        chinese_count = sum(1 for r in regions if r['is_chinese'])
        other_count = len(regions) - chinese_count
        
        print(f"✅ OCR识别完成: 中文区域 {chinese_count} 个, 其他 {other_count} 个")
        return regions
    
    def measure_pixel_height(self, image: np.ndarray, region: Dict) -> Dict:
        """
        动态边界检测：扩展6px，合并所有与原OCR框有重叠的轮廓，取联合外接矩形高度。
        """
        try:
            x, y, w, h = region['bbox']
            text = region['text']

            # 固定扩展6px
            expand = 6
            expanded_x = max(0, x - expand)
            expanded_y = max(0, y - expand)
            expanded_w = min(w + 2 * expand, image.shape[1] - expanded_x)
            expanded_h = min(h + 2 * expand, image.shape[0] - expanded_y)

            # 裁剪扩展后的ROI
            roi_bgr = image[expanded_y:expanded_y+expanded_h, expanded_x:expanded_x+expanded_w]

            print(f"📏 测量区域 '{text}': OCR高度={h}px, 扩充区域={expanded_w}×{expanded_h}px (+6px)")

            # 1. 转换为灰度图
            gray = cv2.cvtColor(roi_bgr, cv2.COLOR_BGR2GRAY)
            # 2. 轻度高斯模糊，降低噪点影响
            blur = cv2.GaussianBlur(gray, (3, 3), 0)
            # 3. Otsu 自适应阈值，自动找最佳分割点
            _, binary = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            # 4. 闭运算填补细小空洞
            kernel = np.ones((3, 3), np.uint8)
            binary_clean = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=1)
            # 5. 查找轮廓
            contours, _ = cv2.findContours(binary_clean, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 合并所有与原OCR框有重叠的轮廓
            main_contours = []
            ocr_box = (expand, expand, w, h)  # 在扩展ROI内的OCR框
            for c in contours:
                x1, y1, w1, h1 = cv2.boundingRect(c)
                # 判断与原OCR框有无重叠
                if not (x1 + w1 < ocr_box[0] or x1 > ocr_box[0] + ocr_box[2] or y1 + h1 < ocr_box[1] or y1 > ocr_box[1] + ocr_box[3]):
                    main_contours.append(c)
            pixel_height = h  # 默认用OCR高度
            contour_bbox = None
            if main_contours:
                all_pts = np.vstack(main_contours)
                cont_x, cont_y, cont_w, cont_h = cv2.boundingRect(all_pts)
                pixel_height = cont_h
                contour_bbox = (cont_x, cont_y, cont_w, cont_h)
                print(f"    → 动态边界: {pixel_height}px (差值: {pixel_height - h:+d}px)")
            else:
                print(f"    → 未找到主轮廓，使用OCR高度: {pixel_height}px")

            # 生成调试图像
            debug_images = self._create_debug_images(
                roi_bgr, gray, blur, binary, binary_clean, main_contours,
                region, contour_bbox
            )

            return {
                'region': region,
                'ocr_height': h,
                'pixel_height': pixel_height,
                'height_diff': pixel_height - h,
                'contour_bbox': contour_bbox,
                'has_contours': bool(main_contours),
                'expanded_region': (expanded_x, expanded_y, expanded_w, expanded_h),
                'expand_pixels': (expand, expand),
                'debug_images': debug_images
            }
        except Exception as e:
            print(f"⚠️ 测量失败 '{region['text']}': {e}")
            return {
                'region': region,
                'ocr_height': region['bbox'][3],
                'pixel_height': region['bbox'][3],
                'height_diff': 0,
                'contour_bbox': None,
                'has_contours': False,
                'expanded_region': region['bbox'],
                'expand_pixels': (expand, expand),
                'debug_images': {}
            }
    
    def _create_debug_images(self, roi_bgr, gray, blur, binary, binary_clean, contours, region, contour_bbox):
        """创建调试图像"""
        debug_images = {}
        
        try:
            # 1. OTSU处理后的二值化图
            otsu_debug = cv2.cvtColor(binary_clean, cv2.COLOR_GRAY2BGR)
            debug_images['otsu'] = otsu_debug
            
            # 2. 轮廓检测图
            contour_debug = roi_bgr.copy()
            if contours:
                # 绘制所有轮廓（绿色）
                cv2.drawContours(contour_debug, contours, -1, (0, 255, 0), 2)
                
                # 绘制合并后的边界框（红色）
                if contour_bbox:
                    cont_x, cont_y, cont_w, cont_h = contour_bbox
                    cv2.rectangle(contour_debug, (cont_x, cont_y), 
                                (cont_x + cont_w, cont_y + cont_h), (0, 0, 255), 2)
            
            debug_images['contour'] = contour_debug
            
        except Exception as e:
            print(f"调试图像生成失败: {e}")
        
        return debug_images
    
    def save_individual_debug_images(self, results: List[Dict]):
        """保存单个区域的调试图像"""
        print(f"\n💾 保存单个区域调试图像...")
        
        for i, result in enumerate(results):
            region = result['region']
            text = region['text']
            debug_images = result.get('debug_images', {})
            
            # 创建安全的文件名
            safe_filename = re.sub(r'[^\w\s-]', '', text).strip()[:10]
            if not safe_filename:
                safe_filename = f"region_{i}"
            
            # 保存OTSU调试图
            if 'otsu' in debug_images:
                otsu_path = os.path.join(self.debug_dir, f"{safe_filename}_otsu.png")
                cv2.imwrite(otsu_path, debug_images['otsu'])
                print(f"  📷 OTSU: {otsu_path}")
            
            # 保存轮廓调试图
            if 'contour' in debug_images:
                contour_path = os.path.join(self.debug_dir, f"{safe_filename}_contour.png")
                cv2.imwrite(contour_path, debug_images['contour'])
                print(f"  📷 轮廓: {contour_path}")
    
    def create_overview_annotation_image(self, image_path: str, results: List[Dict]) -> str:
        """创建整体标注图"""
        print(f"\n🎨 创建整体标注图...")
        
        # 加载原图
        image = cv2.imread(image_path)
        if image is None:
            return ""
        
        # 转换为PIL图像以支持中文显示和透明度
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # 创建透明图层
        overlay = Image.new('RGBA', pil_image.size, (255, 255, 255, 0))
        draw = ImageDraw.Draw(overlay)
        
        # 定义颜色（带透明度）
        colors = [
            (255, 0, 0, 100),    # 红色
            (0, 255, 0, 100),    # 绿色  
            (0, 0, 255, 100),    # 蓝色
            (255, 255, 0, 100),  # 黄色
            (255, 0, 255, 100),  # 紫色
            (0, 255, 255, 100),  # 青色
            (255, 128, 0, 100),  # 橙色
            (128, 0, 255, 100),  # 靛色
        ]
        
        for i, result in enumerate(results):
            region = result['region']
            if not region['is_chinese']:  # 只处理中文区域
                continue
                
            x, y, w, h = region['bbox']
            text = region['text']
            ocr_height = result['ocr_height']
            pixel_height = result['pixel_height']
            height_diff = result['height_diff']
            
            # 获取扩充信息
            expand_pixels = result.get('expand_pixels', (6, 6))
            expanded_region = result.get('expanded_region', (x, y, w, h))
            
            # 选择颜色
            color = colors[i % len(colors)]
            
            # 1. 绘制扩充区域边界框（淡色虚线）
            exp_x, exp_y, exp_w, exp_h = expanded_region
            self._draw_dashed_rectangle(draw, 
                [exp_x, exp_y, exp_x + exp_w, exp_y + exp_h],
                (128, 128, 128, 180), width=1, dash_length=3)
            
            # 2. 绘制OCR识别范围（透明填充）
            draw.rectangle([x, y, x + w, y + h], fill=color, outline=None)
            
            # 3. 绘制OCR边界框（实线）
            draw.rectangle([x, y, x + w, y + h], outline=(color[0], color[1], color[2], 255), width=2)
            
            # 4. 绘制像素级高度范围（虚线框）
            if result['contour_bbox']:
                cont_x, cont_y, cont_w, cont_h = result['contour_bbox']
                # 转换到全图坐标（注意现在是基于扩充区域的）
                abs_cont_x = exp_x + cont_x
                abs_cont_y = exp_y + cont_y
                
                # 绘制虚线框（用短线段模拟）
                self._draw_dashed_rectangle(draw, 
                    [abs_cont_x, abs_cont_y, abs_cont_x + cont_w, abs_cont_y + cont_h],
                    (255, 255, 255, 255), width=2, dash_length=5)
            
            # 5. 添加文字标注
            label_x = x + w + 10
            label_y = y
            
            # 背景框
            expand_w_px, expand_h_px = expand_pixels
            label_text = f"{text}\nOCR: {ocr_height}px\n像素: {pixel_height}px\n差值: {height_diff:+d}px\n扩充: +{expand_w_px}×{expand_h_px}px"
            
            # 计算文字尺寸来绘制背景
            lines = label_text.split('\n')
            max_width = 0
            total_height = 0
            line_height = 18
            
            for line in lines:
                try:
                    bbox = draw.textbbox((0, 0), line, font=self.chinese_font)
                    line_width = bbox[2] - bbox[0]
                    max_width = max(max_width, line_width)
                except:
                    max_width = max(max_width, len(line) * 10)
                total_height += line_height
            
            # 绘制半透明背景
            bg_padding = 5
            draw.rectangle([
                label_x - bg_padding, label_y - bg_padding,
                label_x + max_width + bg_padding, label_y + total_height + bg_padding
            ], fill=(0, 0, 0, 180), outline=(255, 255, 255, 255), width=1)
            
            # 绘制文字
            current_y = label_y
            for line in lines:
                draw.text((label_x, current_y), line, fill=(255, 255, 255, 255), font=self.chinese_font)
                current_y += line_height
        
        # 合并透明图层到原图
        pil_image = pil_image.convert('RGBA')
        result_image = Image.alpha_composite(pil_image, overlay)
        
        # 转换回BGR格式并保存
        final_image = cv2.cvtColor(np.array(result_image.convert('RGB')), cv2.COLOR_RGB2BGR)
        output_path = os.path.join(self.debug_dir, "overview_annotation.png")
        cv2.imwrite(output_path, final_image)
        
        print(f"💾 保存整体标注图: {output_path}")
        return output_path
    
    def _draw_dashed_rectangle(self, draw, coords, color, width=2, dash_length=5):
        """绘制虚线矩形"""
        x1, y1, x2, y2 = coords
        
        # 上边
        self._draw_dashed_line(draw, (x1, y1), (x2, y1), color, width, dash_length)
        # 右边  
        self._draw_dashed_line(draw, (x2, y1), (x2, y2), color, width, dash_length)
        # 下边
        self._draw_dashed_line(draw, (x2, y2), (x1, y2), color, width, dash_length)
        # 左边
        self._draw_dashed_line(draw, (x1, y2), (x1, y1), color, width, dash_length)
    
    def _draw_dashed_line(self, draw, start, end, color, width, dash_length):
        """绘制虚线"""
        x1, y1 = start
        x2, y2 = end
        
        # 计算总长度
        total_length = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5
        if total_length == 0:
            return
        
        # 计算方向向量
        dx = (x2 - x1) / total_length
        dy = (y2 - y1) / total_length
        
        # 绘制虚线段
        current_length = 0
        is_dash = True
        
        while current_length < total_length:
            segment_length = min(dash_length, total_length - current_length)
            
            if is_dash:
                seg_x1 = x1 + current_length * dx
                seg_y1 = y1 + current_length * dy
                seg_x2 = x1 + (current_length + segment_length) * dx
                seg_y2 = y1 + (current_length + segment_length) * dy
                
                draw.line([(seg_x1, seg_y1), (seg_x2, seg_y2)], fill=color, width=width)
            
            current_length += segment_length
            is_dash = not is_dash
    
    def print_measurement_summary(self, results: List[Dict]):
        """打印测量结果摘要"""
        print("\n" + "="*80)
        print("📏 像素级高度测量摘要")
        print("="*80)
        
        chinese_results = [r for r in results if r['region']['is_chinese']]
        
        for i, result in enumerate(chinese_results, 1):
            region = result['region']
            text = region['text']
            ocr_height = result['ocr_height']
            pixel_height = result['pixel_height']
            height_diff = result['height_diff']
            has_contours = result['has_contours']
            
            expand_pixels = result.get('expand_pixels', (6, 6))
            expand_w_px, expand_h_px = expand_pixels
            
            print(f"\n{i}. 📝 文字: {text}")
            print(f"   📊 OCR高度: {ocr_height}px")
            print(f"   🔍 像素级高度: {pixel_height}px (扩充+{expand_w_px}×{expand_h_px}px)")
            print(f"   📈 高度差值: {height_diff:+d}px")
            print(f"   🎯 轮廓检测: {'成功' if has_contours else '失败'}")
            
            # 评估测量质量
            if abs(height_diff) <= 1:
                quality = "优秀 ✅"
            elif abs(height_diff) <= 3:
                quality = "良好 ⚡"
            elif abs(height_diff) <= 5:
                quality = "一般 ⚠️"
            else:
                quality = "较差 ❌"
            
            print(f"   🏆 测量质量: {quality}")
    
    def save_measurement_data(self, results: List[Dict]):
        """保存测量数据到JSON文件"""
        output_data = {
            'test_info': {
                'algorithm': 'OTSU + 轮廓检测',
                'total_regions': len(results),
                'chinese_regions': len([r for r in results if r['region']['is_chinese']])
            },
            'measurements': []
        }
        
        for result in results:
            if result['region']['is_chinese']:
                measurement = {
                    'text': result['region']['text'],
                    'bbox': result['region']['bbox'],
                    'ocr_height': result['ocr_height'],
                    'pixel_height': result['pixel_height'],
                    'height_diff': result['height_diff'],
                    'has_contours': result['has_contours'],
                    'contour_bbox': result['contour_bbox']
                }
                output_data['measurements'].append(measurement)
        
        output_path = os.path.join(self.debug_dir, "measurement_data.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 保存测量数据: {output_path}")
    
    def test_image(self, image_path: str):
        """测试单张图像"""
        print(f"\n🚀 开始像素级高度测量测试: {image_path}")
        
        try:
            # 1. 加载图像
            image = cv2.imread(image_path)
            if image is None:
                print(f"❌ 无法加载图像: {image_path}")
                return
            # 1.5 自动缩放，防止bus error
            image = safe_resize(image)
            
            # 2. 提取文字区域
            regions = self.extract_text_regions(image_path)
            if not regions:
                print("❌ 未检测到文字区域")
                return
            
            # 3. 只处理中文区域
            chinese_regions = [r for r in regions if r['is_chinese']]
            if not chinese_regions:
                print("❌ 未检测到中文区域")
                return
            
            print(f"✅ 检测到 {len(chinese_regions)} 个中文区域")
            
            # 4. 测量每个区域的像素级高度
            results = []
            for region in chinese_regions:
                result = self.measure_pixel_height(image, region)
                results.append(result)
            
            # 5. 生成调试图像
            self.save_individual_debug_images(results)
            self.create_overview_annotation_image(image_path, results)
            
            # 6. 保存测量数据
            self.save_measurement_data(results)
            
            # 7. 打印摘要
            self.print_measurement_summary(results)
            
            print(f"\n✅ 测试完成，所有文件保存在: {self.debug_dir}")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("🔍 像素级高度测量独立测试")
    print("="*50)
    
    # 创建测试器
    tester = PixelHeightMeasurementTester()
    
    # 测试图像

    test_image_path = "../10.jpg"
    
    if not os.path.exists(test_image_path):
        print(f"❌ 测试图像不存在: {test_image_path}")
        return
    
    # 执行测试
    tester.test_image(test_image_path)


if __name__ == "__main__":
    main() 