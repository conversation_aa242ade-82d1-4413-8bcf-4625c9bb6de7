"""
简化版文字颜色识别测试
只使用加权过滤平均色算法（距离背景色越远权重越高）

核心功能：
1. OCR文字识别
2. 智能二值化分离文字和背景
3. 加权过滤平均色算法提取文字颜色
4. 背景色提取
5. 对比度计算

作者：测试开发
"""

import os
import cv2
import numpy as np
import json
from typing import List, Tuple, Dict, Any
from PIL import Image, ImageDraw, ImageFont
from paddleocr import PaddleOCR
from collections import Counter
import colorsys


class ColorRecognitionTester:
    """文字颜色识别测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.ocr = None
        self.debug_dir = "color_debug"
        self.ensure_debug_dir()
        
        # 颜色分析参数
        self.contrast_threshold = 4.5   # 对比度阈值
        
        # 初始化中文字体
        self.init_chinese_font()
        
        print("🎨 颜色识别测试器初始化完成")
    
    def ensure_debug_dir(self):
        """确保调试目录存在"""
        os.makedirs(self.debug_dir, exist_ok=True)
        print(f"📁 调试目录: {self.debug_dir}")
    
    def init_chinese_font(self):
        """初始化中文字体"""
        try:
            # 尝试多个可能的中文字体路径
            font_paths = [
                "../fonts/思源黑体/SourceHanSans-VF.otf",
                "../fonts/NotoSansSC/NotoSansSC-Black.ttf",
                "../fonts/台北黑体/TaipeiSans-Bold.ttf",
                "/System/Library/Fonts/PingFang.ttc",  # macOS系统字体
                "/System/Library/Fonts/Hiragino Sans GB.ttc",  # macOS系统字体
            ]
            
            self.chinese_font = None
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        self.chinese_font = ImageFont.truetype(font_path, 16)
                        print(f"✅ 加载中文字体: {os.path.basename(font_path)}")
                        break
                    except Exception as e:
                        continue
            
            if self.chinese_font is None:
                print("⚠️ 未找到中文字体，使用默认字体")
                self.chinese_font = ImageFont.load_default()
                
        except Exception as e:
            print(f"⚠️ 字体初始化失败: {e}")
            self.chinese_font = ImageFont.load_default()
    
    def get_ocr_instance(self):
        """获取OCR实例"""
        if self.ocr is None:
            print("🔍 初始化PaddleOCR...")
            self.ocr = PaddleOCR(
                use_doc_orientation_classify=False,
                use_doc_unwarping=False,
                use_textline_orientation=False
            )
            print("✅ PaddleOCR初始化完成")
        return self.ocr
    
    def is_chinese_text(self, text: str) -> bool:
        """判断是否为中文文本"""
        import re
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))
    
    def extract_text_regions(self, image_path: str) -> List[Dict]:
        """提取文字区域"""
        print(f"\n🔍 开始OCR识别: {image_path}")
        
        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法加载图像: {image_path}")
        
        # OCR识别
        ocr = self.get_ocr_instance()
        result = ocr.predict(input=image_path)
        
        if not result:
            print("❌ 未检测到文字")
            return []
        
        # 解析OCR结果
        regions = []
        for res in result:
            if 'rec_texts' in res and 'dt_polys' in res:
                for i, (poly, text, score) in enumerate(zip(
                    res['dt_polys'], 
                    res['rec_texts'], 
                    res.get('rec_scores', [1.0] * len(res['rec_texts']))
                )):
                    if score < 0.5:  # 置信度阈值
                        continue
                    
                    # 计算边界框
                    points = np.array(poly, dtype=np.int32)
                    x, y, w, h = cv2.boundingRect(points)
                    
                    # 判断是否为中文
                    is_chinese = self.is_chinese_text(text)
                    
                    region = {
                        'id': len(regions),
                        'text': text,
                        'bbox': (x, y, w, h),
                        'poly': poly,
                        'score': score,
                        'is_chinese': is_chinese,
                        'center': (x + w//2, y + h//2)
                    }
                    regions.append(region)
        
        chinese_count = sum(1 for r in regions if r['is_chinese'])
        other_count = len(regions) - chinese_count
        
        print(f"✅ OCR识别完成: 中文区域 {chinese_count} 个, 其他 {other_count} 个")
        return regions
    
    def _sample_edge_pixels(self, binary_image: np.ndarray) -> np.ndarray:
        """采样边缘像素的二值化值来确定背景"""
        h, w = binary_image.shape
        edge_pixels = []
        
        # 采样边缘的宽度（像素）
        edge_width = max(1, min(3, min(h, w) // 10))  # 边缘宽度1-3像素，根据图像大小调整
        
        # 采样上边缘
        if h > edge_width:
            edge_pixels.extend(binary_image[:edge_width, :].flatten())
        
        # 采样下边缘
        if h > edge_width:
            edge_pixels.extend(binary_image[-edge_width:, :].flatten())
        
        # 采样左边缘（避免重复采样角落）
        if w > edge_width and h > 2 * edge_width:
            edge_pixels.extend(binary_image[edge_width:-edge_width, :edge_width].flatten())
        
        # 采样右边缘（避免重复采样角落）
        if w > edge_width and h > 2 * edge_width:
            edge_pixels.extend(binary_image[edge_width:-edge_width, -edge_width:].flatten())
        
        return np.array(edge_pixels)
    
    def extract_region_colors(self, image: np.ndarray, region: Dict) -> Dict:
        """提取区域颜色（只使用加权过滤平均色算法）"""
        try:
            x, y, w, h = region['bbox']
            text_region = image[y:y+h, x:x+w]
            
            region_id = region['id']
            
            # 1. 灰度化和轻度降噪
            gray = cv2.cvtColor(text_region, cv2.COLOR_BGR2GRAY)
            blur = cv2.GaussianBlur(gray, (3, 3), 0)
            
            # 2. 直接OTSU二值化
            _, binary = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 3. 通过边缘采样确定背景，然后判断文字和背景
            white_pixels = np.sum(binary == 255)
            black_pixels = np.sum(binary == 0)
            
            print(f"    区域 {region_id} ({region['text']}) 像素分析: 白色{white_pixels}, 黑色{black_pixels}")
            
            # 采样边缘像素来确定背景色
            edge_binary_values = self._sample_edge_pixels(binary)
            
            if len(edge_binary_values) > 0:
                # 统计边缘像素的二值化结果，占多数的就是背景
                edge_white_count = np.sum(edge_binary_values == 255)
                edge_black_count = np.sum(edge_binary_values == 0)
                
                print(f"    边缘采样: 白色{edge_white_count}, 黑色{edge_black_count}")
                
                if edge_white_count > edge_black_count:
                    # 边缘主要是白色，说明白色是背景，黑色是文字
                    text_mask = binary == 0
                    bg_mask = binary == 255
                    mask_type = "黑色文字(边缘判断)"
                else:
                    # 边缘主要是黑色，说明黑色是背景，白色是文字
                    text_mask = binary == 255
                    bg_mask = binary == 0
                    mask_type = "白色文字(边缘判断)"
            else:
                # 回退到原来的简单判断
                if white_pixels < black_pixels:
                    text_mask = binary == 255
                    bg_mask = binary == 0
                    mask_type = "白色文字(回退判断)"
                else:
                    text_mask = binary == 0
                    bg_mask = binary == 255
                    mask_type = "黑色文字(回退判断)"
                
            print(f"    判断结果: {mask_type}")
            
            # 用掩码提取原图对应区域的颜色
            text_pixels = text_region[text_mask]
            bg_pixels = text_region[bg_mask]
            
            # 4. 计算背景色（不需要排除任何颜色，不打印调试信息）
            bg_color = self.get_dominant_color(bg_pixels, debug_print=False) if len(bg_pixels) > 0 else (255, 255, 255)
            
            # 5. 计算文字色（加权过滤平均色算法）
            text_color = self.get_weighted_filtered_color(text_pixels, bg_color, debug_print=True) if len(text_pixels) > 0 else (0, 0, 0)
            
            # 计算对比度
            contrast = self.calculate_contrast_ratio(text_color, bg_color)
            
            # 创建调试图像（原图+掩码叠加）
            debug_image = self.create_overlay_debug_image(text_region, binary, text_mask, region['text'])
            
            return {
                'text_color': text_color,
                'background_color': bg_color,
                'contrast_ratio': contrast,
                'text_pixels_count': len(text_pixels),
                'bg_pixels_count': len(bg_pixels),
                'debug_image': debug_image
            }
        except Exception as e:
            print(f"⚠️ 区域 {region['id']} 颜色提取失败: {e}")
            return self.get_default_color_info()
    
    def get_dominant_color(self, pixels: np.ndarray, debug_print: bool = False) -> Tuple[int, int, int]:
        """获取像素区域的主要颜色（简单平均色算法，用于背景色）"""
        if len(pixels) == 0:
            return (255, 255, 255)
        
        # 直接计算平均色
        avg_color = np.mean(pixels, axis=0).astype(int)
        return self.bgr_to_rgb(avg_color)
    
    def get_weighted_filtered_color(self, pixels: np.ndarray, bg_color: Tuple[int, int, int] = None, debug_print: bool = False) -> Tuple[int, int, int]:
        """获取加权过滤平均色算法的结果（距离背景色越远权重越高）"""
        if len(pixels) == 0:
            return (0, 0, 0)
        
        # 如果像素很少，直接用平均色
        if len(pixels) < 30:
            avg_color = np.mean(pixels, axis=0).astype(int)
            return self.bgr_to_rgb(avg_color)
        
        # 如果没有背景色，回退到普通平均色
        if bg_color is None:
            avg_color = np.mean(pixels, axis=0).astype(int)
            return self.bgr_to_rgb(avg_color)
        
        # 颜色量化
        quantized = (pixels // 8) * 8  # 32级量化
        color_tuples = [tuple(pixel) for pixel in quantized]
        color_counter = Counter(color_tuples)
        
        # 获取前5个颜色
        top_colors = color_counter.most_common(5)
        if not top_colors:
            avg_color = np.mean(pixels, axis=0).astype(int)
            return self.bgr_to_rgb(avg_color)
        
        # 背景色转换为BGR用于距离计算
        bg_color_bgr = (bg_color[2], bg_color[1], bg_color[0])
        
        # 过滤出距离背景色足够远的颜色，并计算权重
        valid_colors = []
        total_weight = 0
        distance_threshold = 40
        
        if debug_print:
            print(f"        加权过滤算法分析:")
        
        for color_bgr, count in top_colors:
            # 计算与背景色的距离
            distance = np.sqrt(sum((a - b) ** 2 for a, b in zip(color_bgr, bg_color_bgr)))
            
            if distance > distance_threshold:
                # 距离越远，权重越高（使用更高次幂让权重增长更激进）
                weight = (distance ** 1.5) * count
                valid_colors.append((color_bgr, count, distance, weight))
                total_weight += weight
                
                if debug_print:
                    color_rgb = self.bgr_to_rgb(color_bgr)
                    print(f"          ✅ 保留 RGB{color_rgb} (距离={distance:.1f}, 占比={count/len(color_tuples)*100:.1f}%, 权重={weight:.1f})")
            else:
                if debug_print:
                    color_rgb = self.bgr_to_rgb(color_bgr)
                    print(f"          ❌ 剔除 RGB{color_rgb} (距离={distance:.1f}, 占比={count/len(color_tuples)*100:.1f}%)")
        
        if not valid_colors:
            # 如果没有有效颜色，回退到普通平均色
            if debug_print:
                print(f"        → 回退到普通平均色（无有效颜色）")
            avg_color = np.mean(pixels, axis=0).astype(int)
            return self.bgr_to_rgb(avg_color)
        
        # 加权平均计算
        weighted_sum_b = sum(color[0] * weight for color, _, _, weight in valid_colors)
        weighted_sum_g = sum(color[1] * weight for color, _, _, weight in valid_colors)
        weighted_sum_r = sum(color[2] * weight for color, _, _, weight in valid_colors)
        
        final_color_bgr = (
            int(weighted_sum_b / total_weight),
            int(weighted_sum_g / total_weight),
            int(weighted_sum_r / total_weight)
        )
        
        if debug_print:
            valid_pixels = sum(count for _, count, _, _ in valid_colors)
            print(f"        → 使用加权过滤平均色: {valid_pixels}/{len(pixels)} 像素 ({valid_pixels/len(pixels)*100:.1f}%)")
            
        return self.bgr_to_rgb(final_color_bgr)
    
    def bgr_to_rgb(self, bgr_color) -> tuple:
        """将BGR颜色转换为RGB颜色"""
        try:
            if isinstance(bgr_color, np.ndarray):
                bgr_color = bgr_color.astype(int)
                return (int(bgr_color[2]), int(bgr_color[1]), int(bgr_color[0]))
            elif isinstance(bgr_color, (list, tuple)) and len(bgr_color) >= 3:
                return (int(bgr_color[2]), int(bgr_color[1]), int(bgr_color[0]))
            else:
                return (0, 0, 0)
        except Exception as e:
            print(f"BGR到RGB转换失败: {e}")
            return (0, 0, 0)
    
    def create_overlay_debug_image(self, original: np.ndarray, binary: np.ndarray, text_mask: np.ndarray, text: str) -> np.ndarray:
        """创建原图与掩码叠加的调试图像"""
        h, w = original.shape[:2]
        
        # 创建调试图像：原图 + 掩码叠加
        debug_img = original.copy()
        
        # 创建彩色掩码
        mask_colored = np.zeros((h, w, 3), dtype=np.uint8)
        
        # 文字区域用红色半透明标记
        mask_colored[text_mask] = [0, 0, 255]  # 红色
        
        # 叠加掩码到原图
        alpha = 0.4  # 透明度
        debug_img = cv2.addWeighted(debug_img, 1-alpha, mask_colored, alpha, 0)
        
        return debug_img
    
    def calculate_contrast_ratio(self, color1: Tuple[int, int, int], color2: Tuple[int, int, int]) -> float:
        """计算两种颜色的对比度"""
        def luminance(color):
            r, g, b = [c / 255.0 for c in color]
            return 0.299 * r + 0.587 * g + 0.114 * b
        
        l1 = luminance(color1)
        l2 = luminance(color2)
        
        if l1 > l2:
            return (l1 + 0.05) / (l2 + 0.05)
        else:
            return (l2 + 0.05) / (l1 + 0.05)
    
    def get_default_color_info(self) -> Dict:
        """获取默认颜色信息"""
        return {
            'text_color': (0, 0, 0),
            'background_color': (255, 255, 255),
            'contrast_ratio': 21.0,
            'text_pixels_count': 0,
            'bg_pixels_count': 0,
            'debug_image': None
        }
    
    def create_final_visualization(self, image_path: str, regions: List[Dict], color_results: List[Dict]) -> str:
        """创建最终可视化图像"""
        # 加载原图
        image = cv2.imread(image_path)
        if image is None:
            return ""
        
        # 转换为PIL图像以支持中文显示
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        
        # 为每个区域绘制边界框和颜色信息
        for region, color_info in zip(regions, color_results):
            x, y, w, h = region['bbox']
            text = region['text']
            
            # 绘制边界框
            draw.rectangle([x, y, x + w, y + h], outline=(0, 255, 0), width=2)
            
            # 绘制颜色信息
            text_color = color_info['text_color']
            bg_color = color_info['background_color']
            contrast = color_info['contrast_ratio']
            
            # 在边界框上方显示信息
            info_text = f"{text} | C:{contrast:.1f}"
            draw.text((x, y - 25), info_text, fill=(255, 255, 255), font=self.chinese_font)
            draw.text((x, y - 24), info_text, fill=(0, 0, 0), font=self.chinese_font)
            
            # 在边界框内显示两个颜色块
            color_block_size = 20
            if w > color_block_size * 2 + 10 and h > color_block_size:
                # 左边：文字颜色
                draw.rectangle([x + 5, y + 5, x + 5 + color_block_size, y + 5 + color_block_size], 
                              fill=text_color)
                # 右边：背景颜色
                draw.rectangle([x + 5 + color_block_size + 5, y + 5, 
                               x + 5 + color_block_size * 2 + 5, y + 5 + color_block_size], 
                              fill=bg_color)
        
        # 转换回OpenCV格式并保存
        vis_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        output_path = os.path.join(self.debug_dir, "final_visualization.png")
        cv2.imwrite(output_path, vis_image)
        
        print(f"💾 保存最终可视化: {output_path}")
        return output_path
    
    def print_color_summary(self, regions: List[Dict], color_results: List[Dict]):
        """打印颜色分析摘要"""
        print("\n" + "="*80)
        print("🎨 颜色分析摘要")
        print("="*80)
        
        for region, color_info in zip(regions, color_results):
            text = region['text']
            text_color = color_info['text_color']
            bg_color = color_info['background_color']
            contrast = color_info['contrast_ratio']
            text_pixels = color_info['text_pixels_count']
            bg_pixels = color_info['bg_pixels_count']
            
            print(f"\n📝 文字: {text}")
            print(f"   🎨 文字颜色: RGB{text_color}")
            print(f"   🎨 背景颜色: RGB{bg_color}")
            print(f"   📊 对比度: {contrast:.2f}")
            print(f"   📈 像素统计: 文字 {text_pixels}, 背景 {bg_pixels}")
            
            # 对比度评级
            if contrast >= 7:
                rating = "优秀 ✅"
            elif contrast >= 4.5:
                rating = "良好 ⚡"
            else:
                rating = "不佳 ❌"
            print(f"   🏆 对比度评级: {rating}")
    
    def test_image(self, image_path: str):
        """测试单张图像"""
        print(f"\n🚀 开始测试图像: {image_path}")
        
        try:
            # 加载图像
            image = cv2.imread(image_path)
            if image is None:
                print(f"❌ 无法加载图像: {image_path}")
                return
            
            # 提取文字区域
            regions = self.extract_text_regions(image_path)
            if not regions:
                print("❌ 未检测到文字区域")
                return
            
            # 只处理中文区域
            chinese_regions = [r for r in regions if r['is_chinese']]
            if not chinese_regions:
                print("❌ 未检测到中文区域")
                return
            
            print(f"✅ 检测到 {len(chinese_regions)} 个中文区域")
            
            # 提取颜色信息
            color_results = []
            for region in chinese_regions:
                print(f"\n🔍 分析区域 {region['id']}: {region['text']}")
                color_info = self.extract_region_colors(image, region)
                color_results.append(color_info)
            
            # 创建最终可视化
            self.create_final_visualization(image_path, chinese_regions, color_results)
            
            # 打印摘要
            self.print_color_summary(chinese_regions, color_results)
            
            print(f"\n✅ 测试完成，调试图像保存在: {self.debug_dir}")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("🎨 文字颜色识别测试")
    print("="*50)
    
    # 创建测试器
    tester = ColorRecognitionTester()
    
    # 测试图像
    test_image_path = "../7.jpg"
    
    if not os.path.exists(test_image_path):
        print(f"❌ 测试图像不存在: {test_image_path}")
        return
    
    # 执行测试
    tester.test_image(test_image_path)


if __name__ == "__main__":
    main() 