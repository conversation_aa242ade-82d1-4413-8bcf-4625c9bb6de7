"""
像素级高度测量算法对比分析报告

对比原始固定扩展算法 vs 增强版自适应算法
分析各自的优缺点和适用场景

作者：前辈指导开发
"""

import json
import os
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import numpy as np

class AlgorithmComparisonAnalyzer:
    """算法对比分析器"""
    
    def __init__(self):
        self.original_data_path = "pixel_height_debug/measurement_data.json"
        self.enhanced_data_path = "enhanced_pixel_height_debug/enhanced_measurement_data.json"
        
    def load_measurement_data(self) -> Tuple[Dict, Dict]:
        """加载两个算法的测量数据"""
        original_data = None
        enhanced_data = None
        
        # 加载原始算法数据
        if os.path.exists(self.original_data_path):
            with open(self.original_data_path, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
        
        # 加载增强算法数据
        if os.path.exists(self.enhanced_data_path):
            with open(self.enhanced_data_path, 'r', encoding='utf-8') as f:
                enhanced_data = json.load(f)
        
        return original_data, enhanced_data
    
    def analyze_accuracy(self, original_data: Dict, enhanced_data: Dict) -> Dict:
        """分析测量精度"""
        analysis = {
            'original': {'measurements': [], 'stats': {}},
            'enhanced': {'measurements': [], 'stats': {}}
        }
        
        # 分析原始算法
        if original_data:
            for measurement in original_data['measurements']:
                # 原始算法数据结构中没有is_chinese字段，默认都是中文
                height_diff = abs(measurement['height_diff'])
                analysis['original']['measurements'].append({
                    'text': measurement['text'],
                    'height_diff': height_diff,
                    'ocr_height': measurement['ocr_height'],
                    'pixel_height': measurement['pixel_height']
                })

        # 分析增强算法
        if enhanced_data:
            for measurement in enhanced_data['measurements']:
                if measurement.get('is_chinese', True):  # 默认为True
                    height_diff = abs(measurement['height_diff'])
                    analysis['enhanced']['measurements'].append({
                        'text': measurement['text'],
                        'height_diff': height_diff,
                        'ocr_height': measurement['ocr_height'],
                        'pixel_height': measurement['pixel_height'],
                        'confidence': measurement.get('confidence', 0.0),
                        'text_density': measurement.get('text_density', 0.0)
                    })
        
        # 计算统计数据
        for algo_name in ['original', 'enhanced']:
            measurements = analysis[algo_name]['measurements']
            if measurements:
                height_diffs = [m['height_diff'] for m in measurements]
                analysis[algo_name]['stats'] = {
                    'count': len(measurements),
                    'mean_abs_diff': np.mean(height_diffs),
                    'std_abs_diff': np.std(height_diffs),
                    'max_abs_diff': np.max(height_diffs),
                    'min_abs_diff': np.min(height_diffs),
                    'accuracy_within_2px': sum(1 for d in height_diffs if d <= 2) / len(height_diffs),
                    'accuracy_within_5px': sum(1 for d in height_diffs if d <= 5) / len(height_diffs)
                }
        
        return analysis
    
    def print_comparison_report(self):
        """打印对比分析报告"""
        print("\n" + "="*80)
        print("📊 像素级高度测量算法对比分析报告")
        print("="*80)
        
        # 加载数据
        original_data, enhanced_data = self.load_measurement_data()
        
        if not original_data or not enhanced_data:
            print("❌ 缺少测量数据文件，请先运行两个算法")
            return
        
        # 分析精度
        accuracy_analysis = self.analyze_accuracy(original_data, enhanced_data)
        
        print("\n🎯 算法精度对比:")
        print("-" * 50)
        
        for algo_name, display_name in [('original', '原始固定扩展算法'), ('enhanced', '增强自适应算法')]:
            stats = accuracy_analysis[algo_name]['stats']
            if stats:
                print(f"\n📈 {display_name}:")
                print(f"   测量样本数: {stats['count']}")
                print(f"   平均绝对误差: {stats['mean_abs_diff']:.2f}px")
                print(f"   误差标准差: {stats['std_abs_diff']:.2f}px")
                print(f"   最大误差: {stats['max_abs_diff']:.0f}px")
                print(f"   最小误差: {stats['min_abs_diff']:.0f}px")
                print(f"   2px内精度: {stats['accuracy_within_2px']:.1%}")
                print(f"   5px内精度: {stats['accuracy_within_5px']:.1%}")
        
        print("\n🔍 逐项对比分析:")
        print("-" * 50)
        
        # 逐项对比
        original_measurements = {m['text']: m for m in accuracy_analysis['original']['measurements']}
        enhanced_measurements = {m['text']: m for m in accuracy_analysis['enhanced']['measurements']}
        
        for text in original_measurements.keys():
            if text in enhanced_measurements:
                orig = original_measurements[text]
                enh = enhanced_measurements[text]
                
                print(f"\n📝 '{text}':")
                print(f"   OCR高度: {orig['ocr_height']}px")
                print(f"   原始算法: {orig['pixel_height']}px (误差: {orig['height_diff']:.0f}px)")
                print(f"   增强算法: {enh['pixel_height']}px (误差: {enh['height_diff']:.0f}px, 置信度: {enh['confidence']:.2f})")
                
                # 判断哪个更好
                if enh['height_diff'] < orig['height_diff']:
                    improvement = "✅ 增强算法更准确"
                elif enh['height_diff'] > orig['height_diff']:
                    improvement = "❌ 原始算法更准确"
                else:
                    improvement = "⚖️ 两者相同"
                
                print(f"   结果: {improvement}")
        
        print("\n🏆 算法特点总结:")
        print("-" * 50)
        
        print("\n📊 原始固定扩展算法:")
        print("   ✅ 优点:")
        print("      - 实现简单，计算快速")
        print("      - 对所有文字统一处理")
        print("      - 稳定性较好")
        print("   ❌ 缺点:")
        print("      - 固定6px扩展可能过度或不足")
        print("      - 容易包含UI界面元素")
        print("      - 无法适应不同大小的文字")
        print("      - 缺乏质量评估机制")
        
        print("\n🔍 增强自适应算法:")
        print("   ✅ 优点:")
        print("      - 根据文字大小自适应扩展")
        print("      - 多层次轮廓过滤，减少UI干扰")
        print("      - 提供置信度评估")
        print("      - 文字密度验证机制")
        print("      - 更精确的边界检测")
        print("   ❌ 缺点:")
        print("      - 算法复杂度较高")
        print("      - 计算时间稍长")
        print("      - 参数较多，需要调优")
        
        print("\n🎯 推荐使用场景:")
        print("-" * 50)
        print("📈 增强自适应算法适用于:")
        print("   - 包含UI界面的复杂图像")
        print("   - 文字大小差异较大的场景")
        print("   - 对精度要求较高的应用")
        print("   - 需要质量评估的场景")
        
        print("\n📊 原始固定扩展算法适用于:")
        print("   - 纯文字图像，无复杂背景")
        print("   - 文字大小相对统一")
        print("   - 对速度要求较高的场景")
        print("   - 简单快速的原型验证")


def main():
    """主函数"""
    analyzer = AlgorithmComparisonAnalyzer()
    analyzer.print_comparison_report()


if __name__ == "__main__":
    main()
