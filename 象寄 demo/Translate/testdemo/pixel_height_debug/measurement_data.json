{"test_info": {"algorithm": "OTSU + 轮廓检测", "total_regions": 3, "chinese_regions": 3}, "measurements": [{"text": "可背也可提", "bbox": [608, 182, 481, 105], "ocr_height": 105, "pixel_height": 91, "height_diff": -14, "has_contours": true, "contour_bbox": [12, 11, 468, 91]}, {"text": "轻量便捷", "bbox": [698, 298, 390, 108], "ocr_height": 108, "pixel_height": 91, "height_diff": -17, "has_contours": true, "contour_bbox": [16, 13, 374, 91]}, {"text": "轻轻松松一提就走", "bbox": [617, 535, 467, 61], "ocr_height": 61, "pixel_height": 56, "height_diff": -5, "has_contours": true, "contour_bbox": [11, 7, 461, 56]}]}