"""
增强版像素级高度测量算法
解决固定扩展导致的问题，使用自适应智能边界检测

核心改进：
1. 自适应扩展：根据文字大小动态调整扩展范围
2. 文字区域验证：通过文字密度过滤非文字区域
3. 多层次轮廓分析：区分文字轮廓和UI界面轮廓
4. 边界优化：使用形态学操作精确提取文字边界

作者：前辈指导开发
"""

import os
import cv2
import numpy as np
import json
from typing import List, Tuple, Dict, Any, Optional
from PIL import Image, ImageDraw, ImageFont
from paddleocr import PaddleOCR
import re


def safe_resize(image, max_side=1500):
    h, w = image.shape[:2]
    if max(h, w) > max_side:
        scale = max_side / max(h, w)
        image = cv2.resize(image, (int(w*scale), int(h*scale)), interpolation=cv2.INTER_AREA)
    return image


class EnhancedPixelHeightMeasurer:
    """增强版像素级高度测量器"""
    
    def __init__(self):
        """初始化测量器"""
        self.ocr = None
        self.debug_dir = "enhanced_pixel_height_debug"
        self.ensure_debug_dir()
        
        # 初始化中文字体
        self.init_chinese_font()
        
        print("🔍 增强版像素级高度测量器初始化完成")
    
    def ensure_debug_dir(self):
        """确保调试目录存在"""
        os.makedirs(self.debug_dir, exist_ok=True)
        print(f"📁 调试目录: {self.debug_dir}")
    
    def init_chinese_font(self):
        """初始化中文字体"""
        try:
            # 尝试多个可能的中文字体路径
            font_paths = [
                "../fonts/思源黑体/SourceHanSans-Regular.ttf",
                "../fonts/NotoSansSC/NotoSansSC-Black.ttf", 
                "../fonts/台北黑体/TaipeiSans-Bold.ttf",
                "/System/Library/Fonts/PingFang.ttc",  # macOS系统字体
                "/System/Library/Fonts/Hiragino Sans GB.ttc",  # macOS系统字体
            ]
            
            self.chinese_font = None
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        self.chinese_font = ImageFont.truetype(font_path, 16)
                        print(f"✅ 加载中文字体: {os.path.basename(font_path)}")
                        break
                    except Exception as e:
                        continue
            
            if self.chinese_font is None:
                print("⚠️ 未找到中文字体，使用默认字体")
                self.chinese_font = ImageFont.load_default()
                
        except Exception as e:
            print(f"⚠️ 字体初始化失败: {e}")
            self.chinese_font = ImageFont.load_default()
    
    def get_ocr_instance(self):
        """获取OCR实例"""
        if self.ocr is None:
            print("🔍 初始化PaddleOCR...")
            self.ocr = PaddleOCR(
                use_doc_orientation_classify=False,
                use_doc_unwarping=False,
                use_textline_orientation=False
            )
            print("✅ PaddleOCR初始化完成")
        return self.ocr
    
    def is_chinese_text(self, text: str) -> bool:
        """判断是否为中文文本"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))
    
    def extract_text_regions(self, image_path: str) -> Tuple[List[Dict], np.ndarray]:
        """提取文字区域"""
        print(f"\n🔍 开始OCR识别: {image_path}")
        
        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法加载图像: {image_path}")
        
        # 预处理：安全缩放大尺寸图片
        original_image = image.copy()
        image = safe_resize(image)
        
        # 计算缩放比例
        scale_factor = min(image.shape[0] / original_image.shape[0], image.shape[1] / original_image.shape[1])
        print(f"📏 图像缩放比例: {scale_factor:.3f} (原始: {original_image.shape[:2]}, 缩放后: {image.shape[:2]})")
        
        # 保存临时缩放图像用于OCR
        temp_image_path = "temp_resized_for_ocr.jpg"
        cv2.imwrite(temp_image_path, image)
        
        # OCR识别使用缩放后的图像
        ocr = self.get_ocr_instance()
        result = ocr.predict(input=temp_image_path)
        
        # 清理临时文件
        try:
            os.remove(temp_image_path)
        except:
            pass
        
        if not result:
            print("❌ 未检测到文字")
            return [], original_image
        
        # 解析OCR结果
        regions = []
        for res in result:
            if 'rec_texts' in res and 'dt_polys' in res:
                for i, (poly, text, score) in enumerate(zip(
                    res['dt_polys'], 
                    res['rec_texts'], 
                    res.get('rec_scores', [1.0] * len(res['rec_texts']))
                )):
                    if score < 0.5:  # 置信度阈值
                        continue
                    
                    # 计算边界框
                    points = np.array(poly, dtype=np.int32)
                    x, y, w, h = cv2.boundingRect(points)
                    
                    # 如果图像被缩放了，需要将坐标映射回原始图像
                    if scale_factor < 1.0:
                        x = int(x / scale_factor)
                        y = int(y / scale_factor)
                        w = int(w / scale_factor)
                        h = int(h / scale_factor)
                        # 同时调整多边形坐标
                        poly = poly / scale_factor
                    
                    # 判断是否为中文
                    is_chinese = self.is_chinese_text(text)
                    
                    region = {
                        'id': len(regions),
                        'text': text,
                        'bbox': (x, y, w, h),
                        'poly': poly,
                        'score': score,
                        'is_chinese': is_chinese,
                        'center': (x + w//2, y + h//2),
                        'ocr_height': h,  # 原始OCR高度
                        'scale_factor': scale_factor  # 记录缩放比例
                    }
                    regions.append(region)
        
        chinese_count = sum(1 for r in regions if r['is_chinese'])
        other_count = len(regions) - chinese_count
        
        print(f"✅ OCR识别完成: 中文区域 {chinese_count} 个, 其他 {other_count} 个")
        
        # 返回原始图像和区域信息
        return regions, original_image
    
    def calculate_adaptive_expansion(self, region: Dict) -> Tuple[int, int]:
        """
        自适应扩展计算：根据文字大小和内容动态调整扩展范围
        
        策略：
        1. 基础扩展：文字高度的10-20%
        2. 最小扩展：3px（避免过小）
        3. 最大扩展：12px（避免包含其他元素）
        4. 文字长度调整：长文字适当减少扩展
        """
        x, y, w, h = region['bbox']
        text = region['text']
        
        # 基础扩展：高度的15%
        base_expand = max(3, min(12, int(h * 0.15)))
        
        # 根据文字长度调整
        text_length = len(text)
        if text_length > 8:  # 长文字减少扩展
            length_factor = 0.8
        elif text_length > 4:  # 中等长度
            length_factor = 1.0
        else:  # 短文字可以适当增加
            length_factor = 1.2
        
        # 根据宽高比调整
        aspect_ratio = w / h if h > 0 else 1
        if aspect_ratio > 8:  # 很长的文字，可能是一行文本
            ratio_factor = 0.7
        elif aspect_ratio > 4:  # 较长文字
            ratio_factor = 0.85
        else:  # 正常比例
            ratio_factor = 1.0
        
        # 计算最终扩展
        expand_h = max(3, min(12, int(base_expand * length_factor * ratio_factor)))
        expand_w = max(2, min(8, int(expand_h * 0.8)))  # 水平扩展稍小
        
        return expand_w, expand_h

    def calculate_text_density(self, binary_image: np.ndarray) -> float:
        """
        计算二值图像中的文字密度
        用于区分真实文字区域和UI界面
        """
        total_pixels = binary_image.shape[0] * binary_image.shape[1]
        text_pixels = np.sum(binary_image > 0)
        density = text_pixels / total_pixels if total_pixels > 0 else 0
        return density

    def filter_text_contours(self, contours: List, roi_shape: Tuple[int, int],
                           ocr_box: Tuple[int, int, int, int]) -> List:
        """
        过滤轮廓，保留真实的文字轮廓

        过滤策略：
        1. 面积过滤：太小或太大的轮廓
        2. 形状过滤：长宽比异常的轮廓
        3. 位置过滤：与OCR框重叠度低的轮廓
        4. 密度过滤：内部密度过低的轮廓
        """
        if not contours:
            return []

        roi_h, roi_w = roi_shape
        ocr_x, ocr_y, ocr_w, ocr_h = ocr_box
        ocr_area = ocr_w * ocr_h

        filtered_contours = []

        for contour in contours:
            # 计算轮廓属性
            area = cv2.contourArea(contour)
            x, y, w, h = cv2.boundingRect(contour)

            # 1. 面积过滤
            if area < 10:  # 太小的轮廓
                continue
            if area > roi_w * roi_h * 0.8:  # 太大的轮廓（可能是整个UI）
                continue

            # 2. 形状过滤
            aspect_ratio = w / h if h > 0 else 0
            if aspect_ratio > 20 or aspect_ratio < 0.1:  # 异常长宽比
                continue

            # 3. 位置过滤：计算与OCR框的重叠度
            overlap_x1 = max(x, ocr_x)
            overlap_y1 = max(y, ocr_y)
            overlap_x2 = min(x + w, ocr_x + ocr_w)
            overlap_y2 = min(y + h, ocr_y + ocr_h)

            if overlap_x2 > overlap_x1 and overlap_y2 > overlap_y1:
                overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
                overlap_ratio = overlap_area / min(area, ocr_area)

                # 要求至少有30%的重叠
                if overlap_ratio < 0.3:
                    continue
            else:
                continue  # 没有重叠

            # 4. 轮廓复杂度过滤
            perimeter = cv2.arcLength(contour, True)
            if perimeter > 0:
                compactness = 4 * np.pi * area / (perimeter * perimeter)
                if compactness < 0.1:  # 过于复杂的轮廓
                    continue

            filtered_contours.append(contour)

        return filtered_contours

    def enhance_text_extraction(self, roi_bgr: np.ndarray) -> np.ndarray:
        """
        增强文字提取：多种方法组合提取文字区域
        """
        # 转换为灰度图
        gray = cv2.cvtColor(roi_bgr, cv2.COLOR_BGR2GRAY)

        # 方法1：OTSU阈值
        _, binary_otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # 方法2：自适应阈值
        binary_adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                              cv2.THRESH_BINARY_INV, 11, 2)

        # 方法3：基于梯度的边缘检测
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient = np.sqrt(grad_x**2 + grad_y**2)
        gradient = np.uint8(gradient / gradient.max() * 255)
        _, binary_grad = cv2.threshold(gradient, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # 组合多种方法
        combined = cv2.bitwise_or(binary_otsu, binary_adaptive)
        combined = cv2.bitwise_or(combined, binary_grad)

        # 形态学操作清理
        kernel = np.ones((2, 2), np.uint8)
        combined = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel, iterations=1)
        combined = cv2.morphologyEx(combined, cv2.MORPH_OPEN, kernel, iterations=1)

        return combined

    def measure_enhanced_pixel_height(self, image: np.ndarray, region: Dict) -> Dict:
        """
        增强版像素级高度测量

        核心改进：
        1. 自适应扩展范围
        2. 多层次轮廓分析
        3. 文字密度验证
        4. 智能边界优化
        """
        try:
            x, y, w, h = region['bbox']
            text = region['text']

            # 1. 计算自适应扩展
            expand_w, expand_h = self.calculate_adaptive_expansion(region)

            # 2. 计算扩展后的ROI
            expanded_x = max(0, x - expand_w)
            expanded_y = max(0, y - expand_h)
            expanded_w_size = min(w + 2 * expand_w, image.shape[1] - expanded_x)
            expanded_h_size = min(h + 2 * expand_h, image.shape[0] - expanded_y)

            # 3. 裁剪扩展后的ROI
            roi_bgr = image[expanded_y:expanded_y+expanded_h_size, expanded_x:expanded_x+expanded_w_size]

            print(f"📏 测量区域 '{text}': OCR高度={h}px, 自适应扩充={expanded_w_size}×{expanded_h_size}px (+{expand_w}×{expand_h}px)")

            # 4. 增强文字提取
            binary_enhanced = self.enhance_text_extraction(roi_bgr)

            # 5. 计算文字密度
            text_density = self.calculate_text_density(binary_enhanced)

            # 6. 查找轮廓
            contours, _ = cv2.findContours(binary_enhanced, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 7. 过滤轮廓
            ocr_box_in_roi = (expand_w, expand_h, w, h)  # 在扩展ROI内的OCR框
            filtered_contours = self.filter_text_contours(contours, roi_bgr.shape[:2], ocr_box_in_roi)

            # 8. 计算最终高度
            pixel_height = h  # 默认用OCR高度
            contour_bbox = None
            confidence = 0.5  # 测量置信度

            if filtered_contours:
                # 合并所有有效轮廓
                all_pts = np.vstack(filtered_contours)
                cont_x, cont_y, cont_w, cont_h = cv2.boundingRect(all_pts)

                # 验证合并后的轮廓是否合理
                merged_area = cont_w * cont_h
                ocr_area = w * h
                area_ratio = merged_area / ocr_area if ocr_area > 0 else 1

                # 如果面积比例合理且文字密度足够
                if 0.5 <= area_ratio <= 3.0 and text_density > 0.1:
                    pixel_height = cont_h
                    contour_bbox = (cont_x, cont_y, cont_w, cont_h)
                    confidence = min(0.95, 0.6 + text_density * 0.5)
                    print(f"    → 智能边界: {pixel_height}px (差值: {pixel_height - h:+d}px, 置信度: {confidence:.2f})")
                else:
                    print(f"    → 轮廓不合理 (面积比: {area_ratio:.2f}, 密度: {text_density:.2f})，使用OCR高度: {pixel_height}px")
            else:
                print(f"    → 未找到有效轮廓，使用OCR高度: {pixel_height}px")

            # 9. 生成调试图像
            debug_images = self._create_enhanced_debug_images(
                roi_bgr, binary_enhanced, filtered_contours, region, contour_bbox, text_density
            )

            return {
                'region': region,
                'ocr_height': h,
                'pixel_height': pixel_height,
                'height_diff': pixel_height - h,
                'contour_bbox': contour_bbox,
                'has_contours': bool(filtered_contours),
                'expanded_region': (expanded_x, expanded_y, expanded_w_size, expanded_h_size),
                'expand_pixels': (expand_w, expand_h),
                'text_density': text_density,
                'confidence': confidence,
                'debug_images': debug_images
            }

        except Exception as e:
            print(f"⚠️ 测量失败 '{region['text']}': {e}")
            return {
                'region': region,
                'ocr_height': region['bbox'][3],
                'pixel_height': region['bbox'][3],
                'height_diff': 0,
                'contour_bbox': None,
                'has_contours': False,
                'expanded_region': region['bbox'],
                'expand_pixels': (3, 3),
                'text_density': 0.0,
                'confidence': 0.0,
                'debug_images': {}
            }

    def _create_enhanced_debug_images(self, roi_bgr, binary_enhanced, contours, region, contour_bbox, text_density):
        """创建增强版调试图像"""
        debug_images = {}

        try:
            # 1. 增强二值化图
            enhanced_debug = cv2.cvtColor(binary_enhanced, cv2.COLOR_GRAY2BGR)
            debug_images['enhanced_binary'] = enhanced_debug

            # 2. 轮廓检测图
            contour_debug = roi_bgr.copy()
            if contours:
                # 绘制所有过滤后的轮廓（绿色）
                cv2.drawContours(contour_debug, contours, -1, (0, 255, 0), 2)

                # 绘制合并后的边界框（红色）
                if contour_bbox:
                    cont_x, cont_y, cont_w, cont_h = contour_bbox
                    cv2.rectangle(contour_debug, (cont_x, cont_y),
                                (cont_x + cont_w, cont_y + cont_h), (0, 0, 255), 2)

            # 添加文字密度信息
            cv2.putText(contour_debug, f"Density: {text_density:.3f}",
                       (5, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            debug_images['enhanced_contour'] = contour_debug

        except Exception as e:
            print(f"调试图像生成失败: {e}")

        return debug_images

    def test_image(self, image_path: str):
        """测试单张图像"""
        print(f"\n🚀 开始增强版像素级高度测量测试: {image_path}")

        try:
            # 1. 提取文字区域（内部已处理图像缩放）
            regions, image = self.extract_text_regions(image_path)
            if not regions:
                print("❌ 未检测到文字区域")
                return

            # 2. 只处理中文区域
            chinese_regions = [r for r in regions if r['is_chinese']]
            if not chinese_regions:
                print("❌ 未检测到中文区域")
                return

            print(f"✅ 检测到 {len(chinese_regions)} 个中文区域")

            # 3. 测量每个区域的像素级高度
            results = []
            for region in chinese_regions:
                result = self.measure_enhanced_pixel_height(image, region)
                results.append(result)

            # 4. 生成调试图像
            self.save_enhanced_debug_images(results)
            self.create_enhanced_overview_image(image_path, results)

            # 5. 保存测量数据
            self.save_enhanced_measurement_data(results)

            # 6. 打印摘要
            self.print_enhanced_measurement_summary(results)

            print(f"\n✅ 增强版测试完成，所有文件保存在: {self.debug_dir}")

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

    def save_enhanced_debug_images(self, results: List[Dict]):
        """保存增强版调试图像"""
        print(f"\n💾 保存增强版调试图像...")

        for i, result in enumerate(results):
            region = result['region']
            text = region['text']
            debug_images = result.get('debug_images', {})

            # 创建安全的文件名
            safe_filename = re.sub(r'[^\w\s-]', '', text).strip()[:10]
            if not safe_filename:
                safe_filename = f"region_{i}"

            # 保存增强二值化图
            if 'enhanced_binary' in debug_images:
                binary_path = os.path.join(self.debug_dir, f"{safe_filename}_enhanced_binary.png")
                cv2.imwrite(binary_path, debug_images['enhanced_binary'])
                print(f"  📷 增强二值化: {binary_path}")

            # 保存增强轮廓图
            if 'enhanced_contour' in debug_images:
                contour_path = os.path.join(self.debug_dir, f"{safe_filename}_enhanced_contour.png")
                cv2.imwrite(contour_path, debug_images['enhanced_contour'])
                print(f"  📷 增强轮廓: {contour_path}")

    def print_enhanced_measurement_summary(self, results: List[Dict]):
        """打印增强版测量结果摘要"""
        print("\n" + "="*80)
        print("📏 增强版像素级高度测量摘要")
        print("="*80)

        chinese_results = [r for r in results if r['region']['is_chinese']]

        for i, result in enumerate(chinese_results, 1):
            region = result['region']
            text = region['text']
            ocr_height = result['ocr_height']
            pixel_height = result['pixel_height']
            height_diff = result['height_diff']
            has_contours = result['has_contours']
            text_density = result.get('text_density', 0.0)
            confidence = result.get('confidence', 0.0)

            expand_pixels = result.get('expand_pixels', (3, 3))
            expand_w_px, expand_h_px = expand_pixels

            print(f"\n{i}. 📝 文字: {text}")
            print(f"   📊 OCR高度: {ocr_height}px")
            print(f"   🔍 像素级高度: {pixel_height}px (自适应扩充+{expand_w_px}×{expand_h_px}px)")
            print(f"   📈 高度差值: {height_diff:+d}px")
            print(f"   🎯 轮廓检测: {'成功' if has_contours else '失败'}")
            print(f"   📊 文字密度: {text_density:.3f}")
            print(f"   🎯 测量置信度: {confidence:.3f}")

            # 评估测量质量（考虑置信度）
            if confidence > 0.8 and abs(height_diff) <= 2:
                quality = "优秀 ✅"
            elif confidence > 0.6 and abs(height_diff) <= 4:
                quality = "良好 ⚡"
            elif confidence > 0.4 and abs(height_diff) <= 6:
                quality = "一般 ⚠️"
            else:
                quality = "较差 ❌"

            print(f"   🏆 测量质量: {quality}")

    def save_enhanced_measurement_data(self, results: List[Dict]):
        """保存增强版测量数据"""
        print(f"\n💾 保存增强版测量数据...")

        # 准备数据
        data = {
            'algorithm': 'enhanced_pixel_height_measurement',
            'timestamp': str(np.datetime64('now')),
            'total_regions': len(results),
            'chinese_regions': len([r for r in results if r['region']['is_chinese']]),
            'measurements': []
        }

        for result in results:
            region = result['region']
            measurement = {
                'id': region['id'],
                'text': region['text'],
                'is_chinese': region['is_chinese'],
                'bbox': region['bbox'],
                'ocr_height': result['ocr_height'],
                'pixel_height': result['pixel_height'],
                'height_diff': result['height_diff'],
                'has_contours': result['has_contours'],
                'expanded_region': result['expanded_region'],
                'expand_pixels': result['expand_pixels'],
                'text_density': result.get('text_density', 0.0),
                'confidence': result.get('confidence', 0.0),
                'contour_bbox': result.get('contour_bbox')
            }
            data['measurements'].append(measurement)

        # 保存JSON文件
        json_path = os.path.join(self.debug_dir, "enhanced_measurement_data.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        print(f"  📄 测量数据: {json_path}")

    def create_enhanced_overview_image(self, image_path: str, results: List[Dict]):
        """创建增强版总览标注图像"""
        print(f"\n🖼️ 创建增强版总览标注图像...")

        try:
            # 加载原始图像
            image = cv2.imread(image_path)
            if image is None:
                print(f"❌ 无法加载图像: {image_path}")
                return

            # 创建标注图像
            annotated = image.copy()

            # 标注每个区域
            for result in results:
                region = result['region']
                if not region['is_chinese']:
                    continue

                x, y, w, h = region['bbox']
                text = region['text']
                pixel_height = result['pixel_height']
                height_diff = result['height_diff']
                confidence = result.get('confidence', 0.0)

                # 根据置信度选择颜色
                if confidence > 0.8:
                    color = (0, 255, 0)  # 绿色：高置信度
                elif confidence > 0.6:
                    color = (0, 255, 255)  # 黄色：中等置信度
                else:
                    color = (0, 0, 255)  # 红色：低置信度

                # 绘制OCR边界框
                cv2.rectangle(annotated, (x, y), (x + w, y + h), color, 2)

                # 绘制扩展区域（虚线）
                expanded_region = result.get('expanded_region')
                if expanded_region:
                    ex, ey, ew, eh = expanded_region
                    # 用虚线效果绘制扩展区域
                    for i in range(0, ew, 10):
                        cv2.line(annotated, (ex + i, ey), (ex + min(i + 5, ew), ey), (128, 128, 128), 1)
                        cv2.line(annotated, (ex + i, ey + eh), (ex + min(i + 5, ew), ey + eh), (128, 128, 128), 1)
                    for i in range(0, eh, 10):
                        cv2.line(annotated, (ex, ey + i), (ex, ey + min(i + 5, eh)), (128, 128, 128), 1)
                        cv2.line(annotated, (ex + ew, ey + i), (ex + ew, ey + min(i + 5, eh)), (128, 128, 128), 1)

                # 添加文字标注
                label = f"{text} ({pixel_height}px, {height_diff:+d}px, {confidence:.2f})"

                # 计算文字位置
                label_y = y - 10 if y > 30 else y + h + 20

                # 绘制文字背景
                (label_w, label_h), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 1)
                cv2.rectangle(annotated, (x, label_y - label_h - 5), (x + label_w, label_y + 5), color, -1)

                # 绘制文字
                cv2.putText(annotated, label, (x, label_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

            # 保存标注图像
            overview_path = os.path.join(self.debug_dir, "enhanced_overview_annotated.png")
            cv2.imwrite(overview_path, annotated)
            print(f"  🖼️ 总览标注: {overview_path}")

        except Exception as e:
            print(f"❌ 创建总览图像失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("🔍 增强版像素级高度测量测试")
    print("="*50)

    # 创建测试器
    tester = EnhancedPixelHeightMeasurer()

    # 测试图像
    test_image_path = "../22.png"

    if not os.path.exists(test_image_path):
        print(f"❌ 测试图像不存在: {test_image_path}")
        return

    # 执行测试
    tester.test_image(test_image_path)


if __name__ == "__main__":
    main()
