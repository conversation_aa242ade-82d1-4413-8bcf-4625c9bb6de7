/opt/anaconda3/envs/learn_venv/lib/python3.12/site-packages/paddle/utils/cpp_extension/extension_utils.py:715: UserWarning: No ccache found. Please be aware that recompiling all source files may be required. You can download and install ccache from: https://github.com/ccache/ccache/blob/master/doc/INSTALL.md
  warnings.warn(warning_message)
[33mMKL-DNN is not available. Using `paddle` instead.[0m
[32mCreating model: ('PP-OCRv5_server_det', None)[0m
[32mUsing official model (PP-OCRv5_server_det), the model files will be automatically downloaded and saved in /Users/<USER>/.paddlex/official_models.[0m
布局分析处理器初始化完成 (对齐阈值: 5px, 邻近阈值: 50px)
智能图像翻译测试器初始化完成
使用模型: gemini-2.5-pro
输出目录: testdemo/smart_translation_results

🚀 开始智能图像翻译测试流程
图片: example.jpg
模型: gemini-2.5-pro

开始OCR识别: example.jpg
初始化PaddleOCR...

Fetching 6 files:   0%|          | 0/6 [00:00<?, ?it/s]
Fetching 6 files: 100%|██████████| 6/6 [00:00<00:00, 8221.44it/s]
[32mCreating model: ('PP-OCRv5_server_rec', None)[0m
[32mUsing official model (PP-OCRv5_server_rec), the model files will be automatically downloaded and saved in /Users/<USER>/.paddlex/official_models.[0m

Fetching 6 files:   0%|          | 0/6 [00:00<?, ?it/s]
Fetching 6 files: 100%|██████████| 6/6 [00:00<00:00, 4344.93it/s]
PaddleOCR初始化完成
处理图像: example.jpg
      '深润保湿' 文字色(78, 61, 53), 背景色(224, 221, 222), 深色文字True
      '缓解用酸干燥' 文字色(79, 60, 53), 背景色(209, 207, 207), 深色文字True
      '合' 文字色(78, 67, 63), 背景色(222, 221, 223), 深色文字True
      '8D玻尿酸' 文字色(76, 61, 56), 背景色(207, 204, 204), 深色文字True
      '层层补水，强韧肌底' 文字色(78, 70, 68), 背景色(207, 204, 204), 深色文字True
      '库拉索芦荟叶水' 文字色(75, 61, 56), 背景色(198, 196, 195), 深色文字True
      '缓解干燥，沁润保湿' 文字色(77, 69, 67), 背景色(199, 196, 196), 深色文字True
      '库拉索芦荟提取物' 文字色(75, 62, 56), 背景色(187, 184, 183), 深色文字True
      '1水杨' 文字色(161, 74, 80), 背景色(218, 209, 210), 深色文字True
      '净含量：120ml（60片）' 文字色(177, 144, 145), 背景色(220, 213, 213), 深色文字True
      '舒缓保湿，嫩滑肤质' 文字色(76, 66, 64), 背景色(188, 185, 184), 深色文字True
中文区域:
  '深润保湿' (精确高度: 52px)
  '缓解用酸干燥' (精确高度: 52px)
  '合' (精确高度: 32px)
  '8D玻尿酸' (精确高度: 34px)
  '层层补水，强韧肌底' (精确高度: 20px)
  '库拉索芦荟叶水' (精确高度: 35px)
  '缓解干燥，沁润保湿' (精确高度: 20px)
  '库拉索芦荟提取物' (精确高度: 35px)
  '1水杨' (精确高度: 22px)
  '净含量：120ml（60片）' (精确高度: 19px)
  '舒缓保湿，嫩滑肤质' (精确高度: 20px)
其他区域:
  'tondl'

=== 像素级高度统一 ===
高度组 (19, 20) → 统一为 20px
高度组 (32, 34) → 统一为 34px
  '深润保湿': 52px (无需调整)
  '缓解用酸干燥': 52px (无需调整)
  '合': 32px → 34px
  '8D玻尿酸': 34px (无需调整)
  '层层补水，强韧肌底': 20px (无需调整)
  '库拉索芦荟叶水': 35px (无需调整)
  '缓解干燥，沁润保湿': 20px (无需调整)
  '库拉索芦荟提取物': 35px (无需调整)
  '1水杨': 22px (无需调整)
  '净含量：120ml（60片）': 19px → 20px
  '舒缓保湿，嫩滑肤质': 20px (无需调整)
=== 高度统一完成 ===

  中文区域: 11 个
  其他语言区域: 1 个
识别到 11 个中文区域
识别到 1 个其他语言区域

🎨 开始程序颜色识别分析...
  📝 '深润保湿': 文字色(78, 61, 53), 背景色(224, 221, 222), 深色文字True
  📝 '缓解用酸干燥': 文字色(79, 60, 53), 背景色(209, 207, 207), 深色文字True
  📝 '合': 文字色(78, 67, 63), 背景色(222, 221, 223), 深色文字True
  📝 '8D玻尿酸': 文字色(76, 61, 56), 背景色(207, 204, 204), 深色文字True
  📝 '层层补水，强韧肌底': 文字色(78, 70, 68), 背景色(207, 204, 204), 深色文字True
  📝 '库拉索芦荟叶水': 文字色(75, 61, 56), 背景色(198, 196, 195), 深色文字True
  📝 '缓解干燥，沁润保湿': 文字色(77, 69, 67), 背景色(199, 196, 196), 深色文字True
  📝 '库拉索芦荟提取物': 文字色(75, 62, 56), 背景色(187, 184, 183), 深色文字True
  📝 '1水杨': 文字色(161, 74, 80), 背景色(218, 209, 210), 深色文字True
  📝 '净含量：120ml（60片）': 文字色(177, 144, 145), 背景色(220, 213, 213), 深色文字True
  📝 '舒缓保湿，嫩滑肤质': 文字色(76, 66, 64), 背景色(188, 185, 184), 深色文字True
✅ 颜色识别完成，分析了 11 个文字区域

🤖 正在请求gemini-2.5-pro分析图片...
❌ API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=60)

================================================================================
🤖 Gemini简化分析详细结果
================================================================================

📐 布局分析:
  布局模式: unknown
  主要对齐: unknown

�� 对齐分组详情:

🔤 翻译结果详情:
  需要翻译: 0 个

📈 总体评估:
  置信度: 0.00
  总结: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=60)
================================================================================

🎨 生成布局分析调试图...

🎯 生成显示范围调试图...
⚠️ 没有翻译结果，跳过显示范围调试图生成

🎯 生成最大可用区域调试图...
⚠️ 没有最大可用区域数据，跳过调试图生成
⚠️ Gemini分析结果显示无需翻译的文字
✅ 分析报告已保存: testdemo/smart_translation_results/smart_analysis_report.json

📊 Gemini智能分析结果摘要
============================================================
布局模式: unknown
主要对齐: unknown
需要翻译: 0 个文字
无需翻译: 0 个文字

置信度: 0.00
总结: API请求失败: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=60)
============================================================
