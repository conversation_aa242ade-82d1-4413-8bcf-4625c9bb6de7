# AI_Read 文档

## 项目理解记录

### 当前项目状态
- 这是一个图片翻译程序，使用OCR识别文本，然后进行翻译和重绘

### 用户新需求（2025-07-12）
用户要求重新设计流程，使用Gemini进行智能分析：

1. **OCR识别阶段**：获取文本内容和文本框位置
2. **Gemini分析阶段**：
   - 任务1：对齐和分组分析，输出JSON结果用于程序渲染验证
   - 任务2：识别文字、剔除产品主体文字、识别字号字体颜色、输出翻译结果JSON
3. **擦除阶段**：根据Gemini判断的需要翻译区域进行LaMa擦除
4. **重绘阶段**：根据Gemini的翻译结果重绘译文

### 重要注意事项
- 只翻译中文，其他语言不翻译
- LaMa擦除要在Gemini分析之后进行
- Gemini会帮助判断哪些区域不需要擦除（产品主体文字）

### 修改记录（2025-07-12）
已完成对test_layout_validation.py的修改：

1. **更新Gemini提示词**：
   - 改为两阶段分析：布局分析 + 智能文字识别翻译
   - 新增文字识别验证功能
   - 新增产品主体识别和装饰文字剔除
   - 新增should_erase字段控制擦除行为

2. **修改执行流程**：
   - 根据Gemini的should_erase判断进行选择性擦除
   - 支持Gemini提供的bbox位置信息
   - 优化翻译对象构建逻辑

3. **更新数据结构**：
   - translation_analysis.translation_results替代needs_translation
   - 新增text_recognition和product_analysis字段
   - 增强分析摘要显示

### 错误记录

#### 2025-07-15: 大尺寸图片处理Bus Error问题
**问题描述**: 运行test_pixel_height_measurement.py时出现bus error，程序崩溃
**根本原因**:
1. 代码中有safe_resize函数用于缩放大尺寸图片防止内存问题
2. 但OCR识别仍使用原始图像路径，而像素级测量使用缩放后的图像
3. 导致OCR坐标与实际图像尺寸不匹配，引发内存访问错误

**解决方案**:
1. 修改extract_text_regions函数，在内部进行图像缩放
2. 保存临时缩放图像供OCR使用，确保坐标一致性
3. 将OCR坐标按缩放比例映射回原始图像坐标
4. 返回原始图像供后续像素级测量使用
5. 修改函数返回值为(regions, original_image)元组

### 解决方案记录
- 修改test_layout_validation.py以适应新的Gemini分析流程
- 实现了基于Gemini判断的智能擦除机制
- 修复test_pixel_height_measurement.py的大尺寸图片处理bus error问题

#### 2025-07-15: 增强版像素级高度测量算法
**背景**: 原始固定扩展6px算法存在以下问题：
1. 固定扩展可能过度包含UI界面元素
2. 无法适应不同大小文字的需求
3. 缺乏质量评估机制
4. 容易受到背景干扰

**增强版算法核心改进**:
1. **自适应扩展**: 根据文字高度、长度、宽高比动态计算扩展范围(3-12px)
2. **多层次轮廓过滤**:
   - 面积过滤：排除过小/过大轮廓
   - 形状过滤：排除异常长宽比轮廓
   - 位置过滤：要求与OCR框至少30%重叠
   - 复杂度过滤：排除过于复杂的轮廓
3. **增强文字提取**: 组合OTSU、自适应阈值、梯度检测三种方法
4. **文字密度验证**: 通过密度判断区域是否为真实文字
5. **置信度评估**: 提供测量质量评估

**测试结果对比**:
- 平均绝对误差: 6.25px → 3.38px (减少46%)
- 2px内精度: 12.5% → 37.5% (提升3倍)
- 5px内精度: 50.0% → 87.5% (提升75%)
- 8个测试样本中7个更准确，1个稍差

**推荐使用**: 增强版算法适用于包含UI界面的复杂图像，精度要求高的场景
