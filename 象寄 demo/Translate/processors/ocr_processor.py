"""
OCR处理器
负责图像文字识别和样式分析
"""
import cv2
import os
import json
import re
import numpy as np
from typing import List, Optional, Dict, Any, Tuple
from paddleocr import PaddleOCR
from PIL import Image, ImageDraw, ImageFont
from collections import Counter

from models.data_models import TextRegion, OCRResult, ProcessingResult
from config.settings import get_config_manager


class OCRProcessor:
    """OCR处理器"""
    
    def __init__(self):
        """初始化OCR处理器"""
        self._ocr_instance: Optional[PaddleOCR] = None
        self._chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        self.config_manager = get_config_manager()
    
    def _get_ocr_instance(self) -> Optional[PaddleOCR]:
        """获取OCR实例，避免重复初始化"""
        if self._ocr_instance is None:
            print("初始化PaddleOCR...")
            try:
                self._ocr_instance = PaddleOCR(
                    use_doc_orientation_classify=False,
                    use_doc_unwarping=False,
                    use_textline_orientation=False,
                    lang='ch'  # 明确指定中文识别模型
                )
                print("PaddleOCR初始化完成")
            except Exception as e:
                print(f"PaddleOCR初始化失败: {e}")
                return None
        return self._ocr_instance
    
    def is_chinese_text(self, text: str) -> bool:
        """判断文本是否包含中文字符"""
        return bool(self._chinese_pattern.search(text))
    
    def process_image(self, image_path: str, confidence_threshold: float = 0.5) -> ProcessingResult:
        """
        处理图像进行OCR识别，包含样式分析

        Args:
            image_path: 图像文件路径
            confidence_threshold: 置信度阈值

        Returns:
            ProcessingResult: 包含OCRResult的处理结果
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                return ProcessingResult.error_result(f"图像文件不存在: {image_path}")

            # 获取OCR实例
            ocr = self._get_ocr_instance()
            if ocr is None:
                return ProcessingResult.error_result("OCR初始化失败")

            print(f"处理图像: {image_path}")

            # 加载图像用于样式分析
            image = cv2.imread(image_path)
            if image is None:
                return ProcessingResult.error_result(f"无法加载图像: {image_path}")

            # 预处理图像尺寸，防止Bus Error
            processed_image_path = self._preprocess_image_size(image_path, image)

            # OCR处理
            result = ocr.predict(input=processed_image_path)
            if not result:
                return ProcessingResult.error_result("未检测到文字")

            # 处理OCR结果并进行样式分析
            ocr_result = self._parse_ocr_result_with_style(result, image, confidence_threshold)

            # 文本统计信息已在 _output_text_summary 中输出

            # 生成调试图像和数据（如果启用）
            if self.config_manager.config.enable_ocr_debug:
                self._generate_debug_outputs(image_path, ocr_result, confidence_threshold)

            return ProcessingResult.success_result(ocr_result)

        except Exception as e:
            error_msg = f"OCR处理失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)

    def _preprocess_image_size(self, image_path: str, image: np.ndarray) -> str:
        """
        预处理图像尺寸，防止Bus Error
        
        Args:
            image_path: 原始图像路径
            image: 图像数据
            
        Returns:
            str: 处理后的图像路径（可能是原路径或临时缩放后的路径）
        """
        max_dimension = 1500  # 安全的最大尺寸
        height, width = image.shape[:2]
        
        # 检查是否需要缩放
        if max(height, width) <= max_dimension:
            print(f"  图像尺寸安全: {width}×{height}")
            return image_path
        
        # 计算缩放比例
        scale_factor = max_dimension / max(height, width)
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        
        print(f"  图像尺寸过大: {width}×{height} → 缩放至 {new_width}×{new_height}")
        
        # 缩放图像
        resized_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
        
        # 保存临时缩放图像
        temp_dir = "temp_ocr"
        os.makedirs(temp_dir, exist_ok=True)
        
        # 使用原文件名加后缀
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        ext = os.path.splitext(image_path)[1]
        temp_path = os.path.join(temp_dir, f"{base_name}_resized{ext}")
        
        cv2.imwrite(temp_path, resized_image)
        print(f"  临时缩放图像保存至: {temp_path}")
        
        return temp_path

    def _parse_ocr_result_with_style(self, raw_result, image: np.ndarray, confidence_threshold: float) -> OCRResult:
        """解析OCR结果并进行样式分析"""
        return self._parse_ocr_result_internal(raw_result, confidence_threshold, image)

    def _parse_ocr_result(self, raw_result, confidence_threshold: float) -> OCRResult:
        """解析原始OCR结果"""
        return self._parse_ocr_result_internal(raw_result, confidence_threshold)

    def _parse_ocr_result_internal(self, raw_result, confidence_threshold: float, image: np.ndarray = None) -> OCRResult:
        """内部OCR结果解析方法"""
        chinese_regions = []
        other_regions = []

        # 提取所有检测结果
        all_dt_polys, all_rec_texts, all_rec_scores = self._extract_raw_data(raw_result)

        # 创建文字区域对象
        chinese_texts, other_texts = [], []

        for i, (poly, text, score) in enumerate(zip(all_dt_polys, all_rec_texts, all_rec_scores)):
            if score < confidence_threshold:
                continue

            is_chinese = self.is_chinese_text(text)
            region = TextRegion.from_ocr_result(i, poly, text, score, is_chinese)

            if is_chinese:
                # 样式分析（仅在提供图像时进行）
                if image is not None:
                    try:
                        style_info = self.extract_complete_style(image, region.bbox, text)
                        region.style_info = style_info
                        chinese_texts.append(f"'{text}' (精确高度: {style_info['precise_height']}px)")
                    except Exception as e:
                        chinese_texts.append(f"'{text}' (样式分析失败: {e})")
                else:
                    chinese_texts.append(f"'{text}'")

                chinese_regions.append(region)
            else:
                other_regions.append(region)
                other_texts.append(f"'{text}'")

        # 输出文本信息（包含颜色和高度信息）
        self._output_text_summary(chinese_regions, other_regions)

        # 像素级高度统一（仅在有样式信息时进行）
        if image is not None and len(chinese_regions) > 1:
            chinese_regions = self._unify_pixel_heights(chinese_regions)

        return OCRResult(
            dt_polys=all_dt_polys,
            rec_texts=all_rec_texts,
            rec_scores=all_rec_scores,
            chinese_regions=chinese_regions,
            other_regions=other_regions
        )

    def _extract_raw_data(self, raw_result):
        """提取原始OCR数据"""
        all_dt_polys, all_rec_texts, all_rec_scores = [], [], []

        for res in raw_result:
            if 'rec_texts' in res and 'dt_polys' in res:
                all_dt_polys.extend(res['dt_polys'])
                all_rec_texts.extend(res['rec_texts'])
                all_rec_scores.extend(res.get('rec_scores', [1.0] * len(res['rec_texts'])))

        return all_dt_polys, all_rec_texts, all_rec_scores
    
    def _generate_debug_outputs(self, image_path: str, ocr_result: OCRResult, confidence_threshold: float):
        """生成调试图像和数据文件"""
        try:
            debug_dir = self.config_manager.config.ocr_debug_dir
            os.makedirs(debug_dir, exist_ok=True)
            self._save_height_comparison_debug(image_path, ocr_result, debug_dir)
            self._save_color_analysis_debug(image_path, ocr_result, debug_dir)
            self._save_ocr_data_json(ocr_result, confidence_threshold, debug_dir)
            print(f"OCR调试文件已保存到: {debug_dir}")
        except Exception as e:
            print(f"生成OCR调试文件失败: {e}")

    def _load_chinese_font(self, size: int = 20):
        """加载中文字体"""
        try:
            # 尝试使用系统中文字体
            return ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", size)
        except:
            try:
                # 尝试使用项目中的字体
                font_path = os.path.join(self.config_manager.get_fonts_dir(), "思源黑体", "SourceHanSans-VF.otf")
                return ImageFont.truetype(font_path, size)
            except:
                # 使用默认字体
                return ImageFont.load_default()

    def _draw_label_with_bg(self, draw, pos, text, font, bg_color, text_color, padding=2):
        """在指定位置绘制带背景色的文本（用于序号标注）"""
        x, y = pos
        # 兼容新旧版本PIL的文本尺寸获取
        try:
            # 新版本 PIL (>= 8.0.0)
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        except AttributeError:
            # 旧版本 PIL
            text_width, text_height = draw.textsize(text, font=font)
        
        # 背景框
        draw.rectangle([x, y, x + text_width + 2*padding, y + text_height + 2*padding], fill=bg_color)
        # 文字
        draw.text((x + padding, y + padding), text, fill=text_color, font=font)

    def _save_height_comparison_debug(self, image_path: str, ocr_result: OCRResult, debug_dir: str):
        """保存高度对比调试图像（合并原始和统一后的高度信息，绿色框+序号和高度对比标注）"""
        image = cv2.imread(image_path)
        if image is None:
            return

        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        font = self._load_chinese_font(22)
        font_small = self._load_chinese_font(18)

        for idx, region in enumerate(ocr_result.chinese_regions):
            x, y, w, h = region.bbox
            
            # 获取高度信息
            if region.style_info:
                original_height = region.style_info.get("original_height")
                unified_height = region.style_info.get("precise_height", h)
            else:
                original_height = h
                unified_height = h
            
            # 绿色粗框（统一使用绿色）
            draw.rectangle([x, y, x + w, y + h], outline=(0, 200, 0), width=4)
            
            # 标注信息
            if original_height and original_height != unified_height:
                # 有调整：显示 序号: 原始px→调整后px
                label = f"{idx+1}: {original_height}px→{unified_height}px"
            else:
                # 无调整：显示 序号: 高度px
                label = f"{idx+1}: {unified_height}px"
            
            # 绿底白字标注
            self._draw_label_with_bg(draw, (x, y-28), label, font, (0,200,0), (255,255,255))

        final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        cv2.imwrite(os.path.join(debug_dir, "det_chinese_regions_unified.png"), final_image)


    
    def _save_ocr_data_json(self, ocr_result: OCRResult, confidence_threshold: float, debug_dir: str):
        """保存OCR数据到JSON文件"""
        def convert_numpy_types(obj):
            """转换numpy数据类型为Python原生类型"""
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, (np.int64, np.int32, np.int16, np.int8)):
                return int(obj)
            elif isinstance(obj, (np.float64, np.float32, np.float16)):
                return float(obj)
            elif isinstance(obj, (np.bool_, np.bool8)):
                return bool(obj)
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, (list, tuple)):
                return [convert_numpy_types(item) for item in obj]
            else:
                return obj
        
        # 序列化OCR结果
        ocr_data = {
            "metadata": {
                "timestamp": self._get_timestamp(),
                "confidence_threshold": confidence_threshold,
                "total_regions": ocr_result.total_regions,
                "chinese_count": ocr_result.chinese_count,
                "other_regions_count": len(ocr_result.other_regions)
            },
            "chinese_regions": [self._serialize_region(region, convert_numpy_types, True)
                               for region in ocr_result.chinese_regions],
            "other_regions": [self._serialize_region(region, convert_numpy_types, False)
                             for region in ocr_result.other_regions],
            "raw_data": {
                "dt_polys_count": len(ocr_result.dt_polys),     # 原始检测多边形数量
                "rec_texts": ocr_result.rec_texts,              # 原始识别文本列表
                "rec_scores": ocr_result.rec_scores             # 原始置信度分数列表
            }
        }
        
        # 转换所有numpy类型
        ocr_data = convert_numpy_types(ocr_data)
        
        # 保存JSON文件
        output_path = os.path.join(debug_dir, "ocr_data.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(ocr_data, f, ensure_ascii=False, indent=2)

    def _serialize_region(self, region, convert_func, include_style=False):
        """序列化区域对象"""
        data = {
            "id": region.id,
            "text": region.text,
            "bbox": list(region.bbox),
            "poly": region.poly.tolist(),
            "score": region.score,
            "is_chinese": region.is_chinese,
            "center": list(region.center)
        }
        if include_style:
            data["style_info"] = convert_func(getattr(region, 'style_info', None))
        return data

    def measure_roi_text_height(self, roi_bgr: np.ndarray, debug_prefix: Optional[str] = None) -> int:
        """
        对OCR裁剪区域测真实笔画高度（用于字号计算）

        Args:
            roi_bgr: 裁剪的BGR图像
            debug_prefix: 保留参数以兼容调用，但不再使用

        Returns:
            int: 文字实际高度 (像素)
        """
        try:
            if roi_bgr is None or roi_bgr.size == 0:
                return 0
                
            # 转换为灰度图
            gray = cv2.cvtColor(roi_bgr, cv2.COLOR_BGR2GRAY)

            # 轻度高斯模糊，降低噪点影响
            blur = cv2.GaussianBlur(gray, (3, 3), 0)

            # Otsu 自适应阈值，自动找最佳分割点
            _, binary = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

            # 闭运算填补细小空洞
            kernel = np.ones((3, 3), np.uint8)
            binary_clean = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=1)

            # 查找轮廓
            contours, _ = cv2.findContours(binary_clean, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if not contours:
                # 没有找到文字像素，返回ROI高度
                return roi_bgr.shape[0]

            # 合并所有轮廓点，得到整体文字外框的高度
            all_pts = np.vstack(contours)
            _, _, _, h = cv2.boundingRect(all_pts)
            
            return h

        except Exception as e:
            print(f"高度测量失败: {e}")
            # 返回ROI高度作为兜底
            return roi_bgr.shape[0] if roi_bgr is not None and roi_bgr.size > 0 else 0

    def _sample_edge_pixels(self, binary_image: np.ndarray) -> np.ndarray:
        """采样边缘像素的二值化值来确定背景"""
        h, w = binary_image.shape
        edge_pixels = []
        
        # 采样边缘的宽度（像素）
        edge_width = max(1, min(3, min(h, w) // 10))  # 边缘宽度1-3像素，根据图像大小调整
        
        # 采样上边缘
        if h > edge_width:
            edge_pixels.extend(binary_image[:edge_width, :].flatten())
        
        # 采样下边缘
        if h > edge_width:
            edge_pixels.extend(binary_image[-edge_width:, :].flatten())
        
        # 采样左边缘（避免重复采样角落）
        if w > edge_width and h > 2 * edge_width:
            edge_pixels.extend(binary_image[edge_width:-edge_width, :edge_width].flatten())
        
        # 采样右边缘（避免重复采样角落）
        if w > edge_width and h > 2 * edge_width:
            edge_pixels.extend(binary_image[edge_width:-edge_width, -edge_width:].flatten())
        
        return np.array(edge_pixels)

    def extract_text_foreground_color(self, text_region: np.ndarray) -> tuple:
        """
        优化后的颜色提取方法：基于直接二值化 + 主色提取
        
        核心改进：
        1. 去除冗余的形态学清理（保留文字空洞）
        2. 使用主色而非平均色（更准确）
        3. 智能判断文字和背景区域

        Args:
            text_region: 文字区域图像（BGR）

        Returns:
            tuple: (text_color, bg_color, is_dark_text)
        """
        try:
            if text_region is None or text_region.size == 0:
                return (0, 0, 0), (255, 255, 255), True

            # 1. 灰度化
            gray = cv2.cvtColor(text_region, cv2.COLOR_BGR2GRAY)
            # 2. 轻度高斯模糊降噪（保留细节）
            blur = cv2.GaussianBlur(gray, (3, 3), 0)
            # 3. 直接OTSU二值化（不做形态学清理）
            _, binary = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 4. 通过边缘采样确定背景，然后判断文字和背景
            white_pixels = np.sum(binary == 255)
            black_pixels = np.sum(binary == 0)
            
            # 采样边缘像素来确定背景色
            edge_binary_values = self._sample_edge_pixels(binary)
            
            if len(edge_binary_values) > 0:
                # 统计边缘像素的二值化结果，占多数的就是背景
                edge_white_count = np.sum(edge_binary_values == 255)
                edge_black_count = np.sum(edge_binary_values == 0)
                
                if edge_white_count > edge_black_count:
                    # 边缘主要是白色，说明白色是背景，黑色是文字
                    text_mask = binary == 0
                    bg_mask = binary == 255
                else:
                    # 边缘主要是黑色，说明黑色是背景，白色是文字
                    text_mask = binary == 255
                    bg_mask = binary == 0
            else:
                # 回退到原来的简单判断
                if white_pixels < black_pixels:
                    text_mask = binary == 255
                    bg_mask = binary == 0
                else:
                    text_mask = binary == 0
                    bg_mask = binary == 255
            
            # 5. 用掩码提取原图对应区域的颜色
            text_pixels = text_region[text_mask]
            bg_pixels = text_region[bg_mask]
            
            # 6. 先计算背景色（不需要排除任何颜色，不打印调试信息）
            bg_color = self._get_dominant_color(bg_pixels, debug_print=False) if len(bg_pixels) > 0 else (255, 255, 255)
            
            # 7. 计算文字色（智能排除背景色，不打印调试信息）
            text_color = self._get_dominant_color(text_pixels, bg_color, debug_print=False) if len(text_pixels) > 0 else (0, 0, 0)
            
            # 8. 判断深色/浅色文字
            text_brightness = np.mean(text_color)
            bg_brightness = np.mean(bg_color)
            is_dark_text = text_brightness < bg_brightness
            
            return text_color, bg_color, is_dark_text
        except Exception as e:
            print(f"优化版颜色提取失败: {e}")
            return (0, 0, 0), (255, 255, 255), True
    
    def _extract_text_color_silent(self, text_region: np.ndarray) -> tuple:
        """静默版本的颜色提取方法，不打印调试信息"""
        try:
            if text_region is None or text_region.size == 0:
                return (0, 0, 0), (255, 255, 255), True

            # 1. 灰度化
            gray = cv2.cvtColor(text_region, cv2.COLOR_BGR2GRAY)
            # 2. 轻度高斯模糊降噪（保留细节）
            blur = cv2.GaussianBlur(gray, (3, 3), 0)
            # 3. 直接OTSU二值化（不做形态学清理）
            _, binary = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 4. 通过边缘采样确定背景，然后判断文字和背景
            white_pixels = np.sum(binary == 255)
            black_pixels = np.sum(binary == 0)
            
            # 采样边缘像素来确定背景色
            edge_binary_values = self._sample_edge_pixels(binary)
            
            if len(edge_binary_values) > 0:
                # 统计边缘像素的二值化结果，占多数的就是背景
                edge_white_count = np.sum(edge_binary_values == 255)
                edge_black_count = np.sum(edge_binary_values == 0)
                
                if edge_white_count > edge_black_count:
                    # 边缘主要是白色，说明白色是背景，黑色是文字
                    text_mask = binary == 0
                    bg_mask = binary == 255
                else:
                    # 边缘主要是黑色，说明黑色是背景，白色是文字
                    text_mask = binary == 255
                    bg_mask = binary == 0
            else:
                # 回退到原来的简单判断
                if white_pixels < black_pixels:
                    text_mask = binary == 255
                    bg_mask = binary == 0
                else:
                    text_mask = binary == 0
                    bg_mask = binary == 255
            
            # 5. 用掩码提取原图对应区域的颜色
            text_pixels = text_region[text_mask]
            bg_pixels = text_region[bg_mask]
            
            # 6. 先计算背景色（不需要排除任何颜色，不打印调试信息）
            bg_color = self._get_dominant_color(bg_pixels, debug_print=False) if len(bg_pixels) > 0 else (255, 255, 255)
            
            # 7. 计算文字色（智能排除背景色，不打印调试信息）
            text_color = self._get_dominant_color(text_pixels, bg_color, debug_print=False) if len(text_pixels) > 0 else (0, 0, 0)
            
            # 8. 判断深色/浅色文字
            text_brightness = np.mean(text_color)
            bg_brightness = np.mean(bg_color)
            is_dark_text = text_brightness < bg_brightness
            
            return text_color, bg_color, is_dark_text
        except Exception as e:
            return (0, 0, 0), (255, 255, 255), True
    
    def _bgr_to_rgb(self, bgr_color) -> tuple:
        """将BGR颜色转换为RGB颜色"""
        try:
            if isinstance(bgr_color, np.ndarray):
                bgr_color = bgr_color.astype(int)
                return (int(bgr_color[2]), int(bgr_color[1]), int(bgr_color[0]))
            elif isinstance(bgr_color, (list, tuple)) and len(bgr_color) >= 3:
                return (int(bgr_color[2]), int(bgr_color[1]), int(bgr_color[0]))
            else:
                return (0, 0, 0)
        except Exception as e:
            print(f"BGR到RGB转换失败: {e}")
            return (0, 0, 0)

    def _get_dominant_color(self, pixels: np.ndarray, background_color: tuple = None, debug_print: bool = False) -> Tuple[int, int, int]:
        """获取像素区域的主要颜色 - 加权过滤平均色算法（距离背景色越远权重越高）"""
        if len(pixels) == 0:
            return (0, 0, 0)
        
        # 如果像素很少，直接用平均色
        if len(pixels) < 30:
            avg_color = np.mean(pixels, axis=0).astype(int)
            return self._bgr_to_rgb(avg_color)
        
        # 如果没有背景色，回退到普通平均色
        if background_color is None:
            avg_color = np.mean(pixels, axis=0).astype(int)
            return self._bgr_to_rgb(avg_color)
        
        # 颜色量化
        quantized = (pixels // 8) * 8  # 32级量化
        color_tuples = [tuple(pixel) for pixel in quantized]
        color_counter = Counter(color_tuples)
        
        # 获取前5个颜色
        top_colors = color_counter.most_common(5)
        if not top_colors:
            avg_color = np.mean(pixels, axis=0).astype(int)
            return self._bgr_to_rgb(avg_color)
        
        # 背景色转换为BGR用于距离计算
        bg_color_bgr = (background_color[2], background_color[1], background_color[0])
        
        # 过滤出距离背景色足够远的颜色，并计算权重
        valid_colors = []
        total_weight = 0
        distance_threshold = 40
        
        if debug_print:
            print(f"        加权过滤算法分析:")
        
        for color_bgr, count in top_colors:
            # 计算与背景色的距离
            distance = np.sqrt(sum((a - b) ** 2 for a, b in zip(color_bgr, bg_color_bgr)))
            
            if distance > distance_threshold:
                # 距离越远，权重越高（使用1.5次幂让权重增长适中）
                weight = (distance ** 1.5) * count
                valid_colors.append((color_bgr, count, distance, weight))
                total_weight += weight
                
                if debug_print:
                    color_rgb = self._bgr_to_rgb(color_bgr)
                    print(f"          ✅ 保留 RGB{color_rgb} (距离={distance:.1f}, 占比={count/len(color_tuples)*100:.1f}%, 权重={weight:.1f})")
            else:
                if debug_print:
                    color_rgb = self._bgr_to_rgb(color_bgr)
                    print(f"          ❌ 剔除 RGB{color_rgb} (距离={distance:.1f}, 占比={count/len(color_tuples)*100:.1f}%)")
        
        if not valid_colors:
            # 如果没有有效颜色，回退到普通平均色
            if debug_print:
                print(f"        → 回退到普通平均色（无有效颜色）")
            avg_color = np.mean(pixels, axis=0).astype(int)
            return self._bgr_to_rgb(avg_color)
        
        # 加权平均计算
        weighted_sum_b = sum(color[0] * weight for color, _, _, weight in valid_colors)
        weighted_sum_g = sum(color[1] * weight for color, _, _, weight in valid_colors)
        weighted_sum_r = sum(color[2] * weight for color, _, _, weight in valid_colors)
        
        final_color_bgr = (
            int(weighted_sum_b / total_weight),
            int(weighted_sum_g / total_weight),
            int(weighted_sum_r / total_weight)
        )
        
        if debug_print:
            valid_pixels = sum(count for _, count, _, _ in valid_colors)
            print(f"        → 使用加权过滤平均色: {valid_pixels}/{len(pixels)} 像素 ({valid_pixels/len(pixels)*100:.1f}%)")
            
        return self._bgr_to_rgb(final_color_bgr)

    def extract_complete_style(self, image: np.ndarray, region_bbox: tuple, text: str = "") -> dict:
        """
        完整的样式信息提取（已升级为基于文字像素掩码的主色分析）
        """
        try:
            x, y, w, h = region_bbox
            x = max(0, x)
            y = max(0, y)
            w = min(w, image.shape[1] - x)
            h = min(h, image.shape[0] - y)
            if w <= 0 or h <= 0:
                return self._default_style_info()
            text_region = image[y:y+h, x:x+w]
            # 1. 精确高度测量
            precise_height = self.measure_roi_text_height(text_region, f"style_extract_{x}_{y}")
            # 2. 文字像素掩码主色分析
            text_color, bg_color, is_dark_text = self.extract_text_foreground_color(text_region)
            # 颜色信息将在后续统一输出，这里不输出
            # 3. 估算字体大小（基于精确高度）
            estimated_font_size = max(16, int(precise_height * 0.95))
            # 4. 粗体检测
            is_bold = self._detect_bold_text(text_region)
            # 5. 计算对比度
            contrast_ratio = self._calculate_contrast_ratio(text_color, bg_color)
            return {
                'estimated_font_size': estimated_font_size,
                'precise_height': precise_height,
                'text_color': text_color,
                'color': text_color,
                'background_color': bg_color,
                'is_bold': is_bold,
                'is_dark_text': is_dark_text,
                'contrast_ratio': contrast_ratio,
                'region_width': w,
                'region_height': h
            }
        except Exception as e:
            print(f"样式提取失败: {e}")
            return self._default_style_info()

    def _detect_bold_text(self, text_region: np.ndarray) -> bool:
        """检测是否为粗体文字"""
        try:
            # 转换为灰度图
            if len(text_region.shape) == 3:
                gray = cv2.cvtColor(text_region, cv2.COLOR_BGR2GRAY)
            else:
                gray = text_region.copy()

            # 二值化
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 计算笔画密度
            text_pixels = np.sum(binary == 0)  # 黑色文字像素
            total_pixels = gray.shape[0] * gray.shape[1]
            fill_ratio = text_pixels / total_pixels

            # 形态学操作分析笔画厚度
            kernel = np.ones((2, 2), np.uint8)
            eroded = cv2.erode(binary, kernel, iterations=1)
            eroded_pixels = np.sum(eroded == 0)
            thickness_retention = eroded_pixels / max(text_pixels, 1)

            # 粗体判断：高填充率 + 高厚度保持率
            is_bold = (fill_ratio > 0.25 and thickness_retention > 0.6) or fill_ratio > 0.4

            return is_bold

        except Exception as e:
            print(f"粗体检测失败: {e}")
            return False

    def _calculate_contrast_ratio(self, color1: tuple, color2: tuple) -> float:
        """计算两个颜色之间的对比度"""
        try:
            # 计算相对亮度
            def relative_luminance(rgb):
                r, g, b = [c / 255.0 for c in rgb]
                # 应用gamma校正
                def gamma_correct(c):
                    return c / 12.92 if c <= 0.03928 else ((c + 0.055) / 1.055) ** 2.4

                r, g, b = map(gamma_correct, [r, g, b])
                return 0.2126 * r + 0.7152 * g + 0.0722 * b

            lum1 = relative_luminance(color1)
            lum2 = relative_luminance(color2)

            # 确保较亮的颜色在分子
            lighter = max(lum1, lum2)
            darker = min(lum1, lum2)

            # 计算对比度比率
            contrast_ratio = (lighter + 0.05) / (darker + 0.05)
            return contrast_ratio

        except Exception as e:
            print(f"对比度计算失败: {e}")
            return 1.0

    def _default_style_info(self) -> dict:
        """返回默认样式信息"""
        return {
            'estimated_font_size': 24,
            'precise_height': 24,
            'text_color': (0, 0, 0),
            'background_color': (255, 255, 255),
            'is_bold': False,
            'is_dark_text': True,
            'contrast_ratio': 21.0,
            'region_width': 0,
            'region_height': 0
        }

    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def cleanup(self):
        """清理资源"""
        if self._ocr_instance is not None:
            # PaddleOCR没有显式的清理方法，设置为None让GC处理
            self._ocr_instance = None
            print("OCR实例已清理")
        
        # 清理临时OCR文件
        self._cleanup_temp_files()
    
    def _cleanup_temp_files(self):
        """清理临时OCR文件"""
        temp_dir = "temp_ocr"
        if os.path.exists(temp_dir):
            import shutil
            try:
                shutil.rmtree(temp_dir)
                print(f"清理临时OCR目录: {temp_dir}")
            except Exception as e:
                print(f"清理临时文件失败: {e}")

    def _unify_pixel_heights(self, regions: List[TextRegion]) -> List[TextRegion]:
        """
        统一像素级高度
        
        Args:
            regions: 文字区域列表
            
        Returns:
            List[TextRegion]: 高度统一后的区域列表
        """
        tolerance = 2  # 容忍度：±2px
        
        print(f"\n=== 像素级高度统一 ===")
        
        # 收集所有有效的精确高度
        valid_heights = []
        for region in regions:
            if region.style_info and 'precise_height' in region.style_info:
                height = region.style_info['precise_height']
                if height > 0:
                    valid_heights.append(height)
        
        if len(valid_heights) < 2:
            print("有效高度少于2个，跳过统一处理")
            return regions
        
        # 统一高度的核心逻辑
        unified_heights = self._calculate_unified_heights(valid_heights, tolerance)
        
        # 应用统一后的高度
        updated_regions = []
        for region in regions:
            if region.style_info and 'precise_height' in region.style_info:
                original_height = region.style_info['precise_height']
                unified_height = self._find_unified_height(original_height, unified_heights, tolerance)
                
                if unified_height != original_height:
                    # 创建新的区域对象，更新高度并保存原始高度
                    new_region = TextRegion(
                        id=region.id,
                        text=region.text,
                        bbox=region.bbox,
                        poly=region.poly,
                        score=region.score,
                        is_chinese=region.is_chinese,
                        center=region.center,
                        style_info=region.style_info.copy()
                    )
                    new_region.style_info['original_height'] = original_height  # 保存原始高度
                    new_region.style_info['precise_height'] = unified_height
                    updated_regions.append(new_region)
                else:
                    # 即使无需调整，也保存原始高度信息
                    new_region = TextRegion(
                        id=region.id,
                        text=region.text,
                        bbox=region.bbox,
                        poly=region.poly,
                        score=region.score,
                        is_chinese=region.is_chinese,
                        center=region.center,
                        style_info=region.style_info.copy()
                    )
                    new_region.style_info['original_height'] = original_height  # 保存原始高度
                    updated_regions.append(new_region)
            else:
                updated_regions.append(region)
        
        # 生成高度分组输出
        self._output_height_groups(updated_regions, unified_heights, tolerance)
        
        print("=== 高度统一完成 ===")
        return updated_regions
    
    def _calculate_unified_heights(self, heights: List[int], tolerance: int) -> Dict[int, int]:
        """
        计算统一后的高度映射
        
        Args:
            heights: 原始高度列表
            tolerance: 容忍度
            
        Returns:
            Dict[int, int]: 原始高度到统一高度的映射
        """
        unique_heights = sorted(set(heights))
        unified_mapping = {}
        processed = set()
        
        for height in unique_heights:
            if height in processed:
                continue
            
            # 找到与当前高度在容忍度范围内的所有高度
            similar_heights = [h for h in unique_heights 
                             if abs(h - height) <= tolerance and h not in processed]
            
            if len(similar_heights) == 1:
                # 只有一个高度，不需要统一
                unified_mapping[height] = height
                processed.add(height)
            else:
                # 多个相似高度，需要统一
                unified_height = self._select_unified_height(similar_heights)
                for h in similar_heights:
                    unified_mapping[h] = unified_height
                    processed.add(h)
                
                # 高度组信息将在后续统一输出
        
        return unified_mapping
    
    def _select_unified_height(self, heights: List[int]) -> int:
        """
        选择统一高度
        
        规则：
        1. 偶数优先：如果有偶数，选择最大的偶数
        2. 都是奇数：选择最大的奇数
        
        Args:
            heights: 相似高度列表
            
        Returns:
            int: 选择的统一高度
        """
        if len(heights) == 1:
            return heights[0]
        
        # 分离偶数和奇数
        even_heights = [h for h in heights if h % 2 == 0]
        odd_heights = [h for h in heights if h % 2 == 1]
        
        if even_heights:
            # 有偶数，选择最大的偶数
            return max(even_heights)
        else:
            # 都是奇数，选择最大的奇数
            return max(odd_heights)
    
    def _find_unified_height(self, original_height: int, unified_mapping: Dict[int, int], tolerance: int = None) -> int:
        """查找原始高度对应的统一高度"""
        return unified_mapping.get(original_height, original_height)

    def _save_color_analysis_debug(self, image_path: str, ocr_result: OCRResult, debug_dir: str):
        """保存颜色识别调试图像（只显示颜色方块，不显示文本）"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                return
                
            # 转换为RGB格式用于PIL处理
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)
            
            for idx, region in enumerate(ocr_result.chinese_regions):
                x, y, w, h = region.bbox
                text_region = image[y:y+h, x:x+w]
                
                # 提取文字色和背景色
                text_color, bg_color, is_dark_text, mask = self._debug_extract_color_and_mask(text_region)
                
                # 绘制文字区域边框（蓝色）
                draw.rectangle([x, y, x + w, y + h], outline=(0, 120, 255), width=2)
                
                # 在文字区域旁边绘制颜色方块
                color_box_size = 24
                spacing = 4
                
                # 确定颜色方块的位置（优先显示在文字右侧，如果空间不够则显示在上方）
                img_width, img_height = pil_image.size
                
                # 尝试放在右侧
                if x + w + spacing + color_box_size * 2 + spacing < img_width:
                    # 右侧有空间
                    text_box_x = x + w + spacing
                    bg_box_x = text_box_x + color_box_size + spacing
                    box_y = y + (h - color_box_size) // 2
                else:
                    # 右侧空间不够，放在上方
                    text_box_x = x
                    bg_box_x = text_box_x + color_box_size + spacing
                    box_y = max(0, y - color_box_size - spacing)
                
                # 确保不超出图像边界
                box_y = max(0, min(box_y, img_height - color_box_size))
                
                # 绘制文字色方块（无边框）
                draw.rectangle([text_box_x, box_y, text_box_x + color_box_size, box_y + color_box_size], 
                             fill=text_color)
                
                # 绘制背景色方块（无边框）
                draw.rectangle([bg_box_x, box_y, bg_box_x + color_box_size, box_y + color_box_size], 
                             fill=bg_color)
                
                # 在原图上叠加序号
                draw.text((x + 2, y + 2), str(idx + 1), fill=(255, 255, 0), font=self._load_chinese_font(16))
            
            # 保存调试图像
            final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            debug_path = os.path.join(debug_dir, "color_analysis_debug.png")
            cv2.imwrite(debug_path, final_image)
            print(f"  颜色识别调试图像已保存: {debug_path}")
            
        except Exception as e:
            print(f"保存颜色识别调试图像失败: {e}")

    def _debug_extract_color_and_mask(self, text_region: np.ndarray):
        """调试用：使用优化后的颜色提取算法，返回主色、背景色、深浅、掩码"""
        try:
            # 使用优化后的颜色提取方法（不打印调试信息）
            text_color, bg_color, is_dark_text = self._extract_text_color_silent(text_region)
            
            # 生成二值化掩码用于调试显示
            gray = cv2.cvtColor(text_region, cv2.COLOR_BGR2GRAY)
            blur = cv2.GaussianBlur(gray, (3, 3), 0)
            _, binary = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 智能判断文字和背景，生成掩码
            white_pixels = np.sum(binary == 255)
            black_pixels = np.sum(binary == 0)
            
            if white_pixels < black_pixels:
                # 白色是文字
                mask = (binary == 255).astype(np.uint8)
            else:
                # 黑色是文字
                mask = (binary == 0).astype(np.uint8)
            
            return text_color, bg_color, is_dark_text, mask
        except Exception as e:
            print(f"调试颜色提取失败: {e}")
            mask = np.zeros((text_region.shape[0], text_region.shape[1]), dtype=np.uint8)
            return (0, 0, 0), (255, 255, 255), True, mask

    def _output_text_summary(self, chinese_regions: List[TextRegion], other_regions: List[TextRegion]):
        """输出文本汇总信息，包括颜色和高度信息"""
        # 输出中文区域信息（包含颜色信息）
        if chinese_regions:
            index = 1
            for region in chinese_regions:
                if region.style_info:
                    text_color = region.style_info.get('text_color', (0, 0, 0))
                    bg_color = region.style_info.get('background_color', (255, 255, 255))
                    is_dark_text = region.style_info.get('is_dark_text', True)
                    precise_height = region.style_info.get('precise_height', 
                                                         region.style_info.get('original_height', 
                                                                              region.bbox[3] if region.bbox else 0))
                    
                    # 只显示前5个字符
                    display_text = region.text[:5] if len(region.text) > 5 else region.text
                    print(f"      {index:<3} '{display_text:<8}' 文字色{text_color} 深色文字{is_dark_text}")
                else:
                    print(f"      {index:<3} '{region.text:<8}' 非中文")
                index += 1
                
        # 输出其他语言区域信息
        if other_regions:
            for region in other_regions:
                print(f"      {index}\t'{region.text}'\t非中文")
                index += 1

        # 移除中文区域和其他区域的重复输出

    def _output_height_groups(self, regions: List[TextRegion], unified_heights: Dict[int, int], tolerance: int):
        """输出高度分组信息"""
        # 创建高度分组
        height_groups = {}
        for region in regions:
            if region.style_info and 'precise_height' in region.style_info:
                precise_height = region.style_info['precise_height']
                original_height = region.style_info.get('original_height', precise_height)
                
                if precise_height not in height_groups:
                    height_groups[precise_height] = []
                
                # 只显示前5个字符
                display_text = region.text[:5] if len(region.text) > 5 else region.text
                height_groups[precise_height].append({
                    'text': display_text,
                    'original_height': original_height,
                    'ocr_height': region.bbox[3] if region.bbox else 0
                })
        
        # 按高度分组输出
        for unified_height in sorted(height_groups.keys()):
            texts_info = height_groups[unified_height]
            
            # 判断是否为分组（有多个文本或有调整）
            has_adjustment = any(info['original_height'] != unified_height for info in texts_info)
            is_group = len(texts_info) > 1 or has_adjustment
            
            if is_group:
                group_label = f"{unified_height}px 分组："
            else:
                group_label = f"{unified_height}px 未分组："
            
            # 生成该分组下的文本信息
            text_entries = []
            for info in texts_info:
                if info['original_height'] == unified_height:
                    # 无需调整的情况
                    text_entries.append(f"'{info['text']}'(原始:{info['original_height']}px, OCR:{info['ocr_height']}px)")
                else:
                    # 需要调整的情况
                    text_entries.append(f"'{info['text']}'(原始:{info['original_height']}px→{unified_height}px, OCR:{info['ocr_height']}px)")
            
            # 输出该分组，使用固定宽度格式
            if text_entries:
                texts_str = ' '.join(text_entries)
                print(f"{group_label:<12} {texts_str}")
