"""
第4次迭代优化的布局识别算法
生成时间: 2025-07-15T21:46:46.652387
"""

import math
import numpy as np
from sklearn.cluster import DBSCAN

def are_styles_similar_final_optimized(item1, item2, height_abs_thresh_strict, height_abs_thresh_loose, height_rel_thresh, color_thresh):
    """
    判断两个文本项的样式是否相似（最终优化版）。
    采用更严格的混合高度判断逻辑和更保守的颜色阈值。

    Args:
        item1 (dict): 第一个文本项, e.g., {'precise_height': 20, 'text_color': (0,0,0)}
        item2 (dict): 第二个文本项
        height_abs_thresh_strict (int): 严格的高度绝对差值阈值，用于捕捉微小误差。
        height_abs_thresh_loose (int): 宽松的高度绝对差值阈值，需与相对阈值配合使用。
        height_rel_thresh (float): 高度相对差值阈值。
        color_thresh (float): 颜色欧氏距离阈值。

    Returns:
        bool: 如果样式相似则返回True，否则返回False。
    """
    h1 = item1['precise_height']
    h2 = item2['precise_height']
    c1 = item1['text_color']
    c2 = item2['text_color']

    # 1. 检查颜色相似性 (欧氏距离) - 优先判断，使用更严格的阈值
    color_dist = math.sqrt(sum([(a - b) ** 2 for a, b in zip(c1, c2)]))
    if color_dist > color_thresh:
        return False

    # 2. 检查高度相似性 (混合逻辑)
    height_diff = abs(h1 - h2)
    
    # 满足极小绝对差异，直接判定为相似 (处理OCR抖动)
    if height_diff <= height_abs_thresh_strict:
        return True
        
    # 否则，必须同时满足较大的绝对差异和相对差异阈值
    # 避免除以零错误
    if max(h1, h2) == 0:
        is_height_similar = height_diff <= height_abs_thresh_loose
    else:
        is_height_similar = (height_diff <= height_abs_thresh_loose) and \
                            (height_diff / max(h1, h2) <= height_rel_thresh)

    return is_height_similar

def group_text_styles_final_optimized(text_items, height_abs_thresh_strict=3, height_abs_thresh_loose=6, height_rel_thresh=0.12, color_thresh=40):
    """
    使用DBSCAN和最终优化的自定义距离矩阵进行文字样式分组。
    优化点:
    - color_thresh (40): 从60大幅降低到40，优先保证“不过粗”，解决Image 3问题。
    - 采用混合高度判断逻辑，解决Image 6的链式合并问题。
      - height_abs_thresh_strict (3): 容忍3px内的OCR抖动。
      - height_abs_thresh_loose (6): 较大的绝对容忍度。
      - height_rel_thresh (0.12): 更严格的相对容忍度。
      - 两者结合使用，避免了之前`or`逻辑的漏洞。

    Args:
        text_items (list of dict): 包含所有文本区域数据的列表。
        ... (其他参数见are_styles_similar_final_optimized)

    Returns:
        list of list: 分组后的结果。
    """
    if not text_items or len(text_items) < 2:
        return [[item] for item in text_items]

    num_items = len(text_items)
    
    # 1. 预计算距离矩阵
    distance_matrix = np.full((num_items, num_items), 1.0)

    for i in range(num_items):
        distance_matrix[i, i] = 0.0
        for j in range(i + 1, num_items):
            if are_styles_similar_final_optimized(text_items[i], text_items[j], 
                                                  height_abs_thresh_strict, 
                                                  height_abs_thresh_loose, 
                                                  height_rel_thresh, 
                                                  color_thresh):
                distance_matrix[i, j] = 0.5
                distance_matrix[j, i] = 0.5

    # 2. 应用DBSCAN
    db = DBSCAN(eps=0.7, min_samples=1, metric='precomputed')
    db.fit(distance_matrix)
    
    # 3. 根据DBSCAN的标签重构分组
    labels = db.labels_
    groups = {}
    for i, label in enumerate(labels):
        if label not in groups:
            groups[label] = []
        groups[label].append(text_items[i])
        
    return list(groups.values())
