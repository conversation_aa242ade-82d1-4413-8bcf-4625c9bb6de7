"""
第1次迭代优化的布局识别算法
生成时间: 2025-07-15T21:35:01.971591
"""

import math

def calculate_color_distance(color1, color2):
    """
    计算两个RGB颜色之间的欧氏距离。
    """
    r1, g1, b1 = color1
    r2, g2, b2 = color2
    return math.sqrt((r1 - r2)**2 + (g1 - g2)**2 + (b1 - b2)**2)

def are_styles_similar(item1, item2, height_abs_thresh, height_rel_thresh, color_thresh):
    """
    判断两个文本项的样式是否相似。
    
    Args:
        item1 (dict): 第一个文本项, e.g., {'precise_height': 20, 'text_color': (0,0,0)}
        item2 (dict): 第二个文本项
        height_abs_thresh (int): 高度绝对差值阈值
        height_rel_thresh (float): 高度相对差值阈值
        color_thresh (float): 颜色欧氏距离阈值
        
    Returns:
        bool: 如果样式相似则返回True，否则返回False。
    """
    h1 = item1['precise_height']
    h2 = item2['precise_height']
    c1 = item1['text_color']
    c2 = item2['text_color']

    # 1. 检查高度相似性 (绝对或相对)
    height_diff = abs(h1 - h2)
    # 避免除以零
    max_h = max(h1, h2)
    if max_h == 0:
        is_height_similar = height_diff <= height_abs_thresh
    else:
        is_height_similar = (height_diff <= height_abs_thresh) or \
                            (height_diff / max_h <= height_rel_thresh)

    if not is_height_similar:
        return False

    # 2. 检查颜色相似性
    color_dist = calculate_color_distance(c1, c2)
    is_color_similar = color_dist <= color_thresh
    
    return is_color_similar

def group_text_styles_optimized(text_items, height_abs_thresh=3, height_rel_thresh=0.15, color_thresh=60):
    """
    优化的文字样式分组算法。

    Args:
        text_items (list of dict): 包含所有文本区域数据的列表。
            每个dict应包含 'text' (str), 'precise_height' (int), 'text_color' (tuple) 等键。
        height_abs_thresh (int): 高度绝对差值阈值。
        height_rel_thresh (float): 高度相对差值阈值。
        color_thresh (float): 颜色欧氏距离阈值。

    Returns:
        list of list: 分组后的结果，每个子列表是一个样式组。
    """
    if not text_items:
        return []

    num_items = len(text_items)
    processed = [False] * num_items
    groups = []

    for i in range(num_items):
        if processed[i]:
            continue

        # 使用当前项作为新组的种子
        seed_item = text_items[i]
        current_group = [seed_item]
        processed[i] = True

        # 遍历剩余未处理的项，看是否能加入当前组
        for j in range(i + 1, num_items):
            if processed[j]:
                continue
            
            candidate_item = text_items[j]
            if are_styles_similar(seed_item, candidate_item, height_abs_thresh, height_rel_thresh, color_thresh):
                current_group.append(candidate_item)
                processed[j] = True
        
        groups.append(current_group)

    return groups

# 示例：使用图片2的数据进行测试
# text_items_img2 = [
#     {'text': "*产品尺寸为手工测量，以实际产品为准", 'precise_height': 20, 'text_color': (120, 112, 104)},
#     {'text': "产品名称", 'precise_height': 20, 'text_color': (66, 54, 44)},
#     {'text': "尺寸", 'precise_height': 20, 'text_color': (77, 65, 53)},
#     {'text': "吸盘兔型湿厕巾收纳架", 'precise_height': 28, 'text_color': (64, 56, 48)},
#     {'text': "卷纸款：15.3*6*24cm", 'precise_height': 28, 'text_color': (58, 50, 42)},
#     {'text': "湿巾款：15.3*12.6*11cm", 'precise_height': 28, 'text_color': (55, 45, 35)},
#     {'text': "产品材质", 'precise_height': 20, 'text_color': (72, 62, 52)},
#     {'text': "颜色", 'precise_height': 20, 'text_color': (84, 72, 61)},
#     {'text': "不锈钢+ABS", 'precise_height': 28, 'text_color': (58, 50, 41)},
#     {'text': "胡桃木色", 'precise_height': 28, 'text_color': (62, 54, 46)},
# ]
# 
# optimized_groups = group_text_styles_optimized(text_items_img2)
# for i, group in enumerate(optimized_groups):
#     print(f"组 {i+1}:")
#     for item in group:
#         print(f"  - \"{item['text']}\" (高度:{item['precise_height']}px, 颜色:{item['text_color']})")
