"""
第2次迭代优化的布局识别算法
生成时间: 2025-07-15T21:38:36.019836
"""

import math
import numpy as np
from sklearn.cluster import DBSCAN

def are_styles_similar_v2(item1, item2, height_abs_thresh, height_rel_thresh, color_thresh):
    """
    判断两个文本项的样式是否相似（优化版）。
    
    Args:
        item1 (dict): 第一个文本项, e.g., {'precise_height': 20, 'text_color': (0,0,0)}
        item2 (dict): 第二个文本项
        height_abs_thresh (int): 高度绝对差值阈值
        height_rel_thresh (float): 高度相对差值阈值
        color_thresh (float): 颜色欧氏距离阈值
        
    Returns:
        bool: 如果样式相似则返回True，否则返回False。
    """
    h1 = item1['precise_height']
    h2 = item2['precise_height']
    c1 = item1['text_color']
    c2 = item2['text_color']

    # 1. 检查颜色相似性 (优先判断，更严格)
    r1, g1, b1 = c1
    r2, g2, b2 = c2
    color_dist = math.sqrt((r1 - r2)**2 + (g1 - g2)**2 + (b1 - b2)**2)
    if color_dist > color_thresh:
        return False

    # 2. 检查高度相似性 (绝对或相对)
    height_diff = abs(h1 - h2)
    max_h = max(h1, h2)
    if max_h == 0: # 避免除以零
        is_height_similar = height_diff <= height_abs_thresh
    else:
        # 必须同时满足绝对和相对阈值中的一个
        is_height_similar = (height_diff <= height_abs_thresh) or \
                            (height_diff / max_h <= height_rel_thresh)

    return is_height_similar

def group_text_styles_dbscan(text_items, height_abs_thresh=4, height_rel_thresh=0.20, color_thresh=35):
    """
    使用DBSCAN和自定义距离矩阵进行优化的文字样式分组算法。

    Args:
        text_items (list of dict): 包含所有文本区域数据的列表。
            每个dict应包含 'text' (str), 'precise_height' (int), 'text_color' (tuple) 等键。
        height_abs_thresh (int): 高度绝对差值阈值。
        height_rel_thresh (float): 高度相对差值阈值。
        color_thresh (float): 颜色欧氏距离阈值。

    Returns:
        list of list: 分组后的结果，每个子列表是一个样式组。
    """
    if not text_items:
        return []

    num_items = len(text_items)
    
    # 1. 预计算距离矩阵
    # 如果样式相似，距离为0.5；否则为1.0。
    # DBSCAN的eps设为0.7，可以确保只有相似的项被聚类。
    distance_matrix = np.full((num_items, num_items), 1.0)

    for i in range(num_items):
        distance_matrix[i, i] = 0.0 # 自身距离为0
        for j in range(i + 1, num_items):
            if are_styles_similar_v2(text_items[i], text_items[j], height_abs_thresh, height_rel_thresh, color_thresh):
                distance_matrix[i, j] = 0.5
                distance_matrix[j, i] = 0.5

    # 2. 应用DBSCAN
    # eps: The maximum distance between two samples for one to be considered as in the neighborhood of the other.
    # min_samples: The number of samples in a neighborhood for a point to be considered as a core point.
    # metric='precomputed': 使用我们自己计算的距离矩阵
    db = DBSCAN(eps=0.7, min_samples=1, metric='precomputed')
    db.fit(distance_matrix)
    
    # 3. 根据DBSCAN的标签重构分组
    labels = db.labels_
    groups = {}
    for i, label in enumerate(labels):
        if label not in groups:
            groups[label] = []
        groups[label].append(text_items[i])
        
    return list(groups.values())
