{"algorithm_type": "tolerance", "total_images": 10, "successful_analyses": 10, "failed_analyses": 0, "image_results": [{"image_path": "gemini_images/1.jpg", "image_name": "1.jpg", "text_regions_count": 7, "total_groups": 3, "single_text_groups": 0, "multi_text_groups": 3, "max_group_size": 3, "style_groups": [{"group_id": 1, "texts": ["4D高回弹记忆棉", "久睡不塌", "适用更久"], "size": 3, "height_variance": 0.0, "color_variance": 0.14814814814814817, "representative_style": {"height": 54, "color": [88, 62, 36]}}, {"group_id": 2, "texts": ["“0压感”", "高回弹海绵"], "size": 2, "height_variance": 0.0, "color_variance": 0.16666666666666666, "representative_style": {"height": 24, "color": [86, 62, 37]}}, {"group_id": 3, "texts": ["深度分散压力", "不易塌陷"], "size": 2, "height_variance": 0.0, "color_variance": 12.416666666666666, "representative_style": {"height": 20, "color": [154, 139, 122]}}], "text_regions": [{"text": "4D高回弹记忆棉", "bbox": [187, 17, 425, 63], "confidence": 0.9934712648391724, "precise_height": 54, "text_color": [88, 62, 36]}, {"text": "久睡不塌", "bbox": [160, 92, 239, 64], "confidence": 0.9993364214897156, "precise_height": 54, "text_color": [88, 61, 37]}, {"text": "适用更久", "bbox": [401, 93, 238, 61], "confidence": 0.9913468360900879, "precise_height": 54, "text_color": [88, 62, 36]}, {"text": "“0压感”", "bbox": [118, 566, 94, 35], "confidence": 0.979945182800293, "precise_height": 24, "text_color": [86, 62, 37]}, {"text": "高回弹海绵", "bbox": [129, 605, 129, 29], "confidence": 0.997807502746582, "precise_height": 24, "text_color": [86, 61, 36]}, {"text": "深度分散压力", "bbox": [129, 691, 137, 28], "confidence": 0.9990137219429016, "precise_height": 20, "text_color": [154, 139, 122]}, {"text": "不易塌陷", "bbox": [131, 721, 91, 26], "confidence": 0.9993071556091309, "precise_height": 20, "text_color": [160, 146, 130]}]}, {"image_path": "gemini_images/10.png", "image_name": "10.png", "text_regions_count": 3, "total_groups": 3, "single_text_groups": 3, "multi_text_groups": 0, "max_group_size": 1, "style_groups": [{"group_id": 1, "texts": ["可背也可提"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 58, "color": [0, 1, 8]}}, {"group_id": 2, "texts": ["轻量便捷"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 92, "color": [0, 2, 9]}}, {"group_id": 3, "texts": ["轻轻松松一提就走"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 5, "color": [14, 42, 54]}}], "text_regions": [{"text": "可背也可提", "bbox": [517, 155, 409, 90], "confidence": 0.9999215006828308, "precise_height": 58, "text_color": [0, 1, 8]}, {"text": "轻量便捷", "bbox": [594, 254, 332, 92], "confidence": 0.999590277671814, "precise_height": 92, "text_color": [0, 2, 9]}, {"text": "轻轻松松一提就走", "bbox": [525, 455, 397, 52], "confidence": 0.9978584051132202, "precise_height": 5, "text_color": [14, 42, 54]}]}, {"image_path": "gemini_images/2.jpg", "image_name": "2.jpg", "text_regions_count": 10, "total_groups": 5, "single_text_groups": 2, "multi_text_groups": 3, "max_group_size": 4, "style_groups": [{"group_id": 1, "texts": ["*产品尺寸为手工测量，以实际产品为准"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 20, "color": [120, 112, 104]}}, {"group_id": 2, "texts": ["产品名称", "产品材质"], "size": 2, "height_variance": 0.0, "color_variance": 13.666666666666666, "representative_style": {"height": 20, "color": [66, 54, 44]}}, {"group_id": 3, "texts": ["尺寸", "颜色"], "size": 2, "height_variance": 0.0, "color_variance": 13.5, "representative_style": {"height": 20, "color": [77, 65, 53]}}, {"group_id": 4, "texts": ["吸盘兔型湿厕巾收纳架", "卷纸款：15.3*6*24cm", "不锈钢+ABS", "胡桃木色"], "size": 4, "height_variance": 0.0, "color_variance": 7.229166666666667, "representative_style": {"height": 28, "color": [64, 56, 48]}}, {"group_id": 5, "texts": ["湿巾款：15.3*12.6*11cm"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 28, "color": [55, 45, 35]}}], "text_regions": [{"text": "*产品尺寸为手工测量，以实际产品为准", "bbox": [219, 418, 356, 23], "confidence": 0.9836475849151611, "precise_height": 20, "text_color": [120, 112, 104]}, {"text": "产品名称", "bbox": [118, 556, 99, 33], "confidence": 0.9999468326568604, "precise_height": 20, "text_color": [66, 54, 44]}, {"text": "尺寸", "bbox": [440, 555, 61, 36], "confidence": 0.9994177222251892, "precise_height": 20, "text_color": [77, 65, 53]}, {"text": "吸盘兔型湿厕巾收纳架", "bbox": [121, 602, 281, 30], "confidence": 0.999004065990448, "precise_height": 28, "text_color": [64, 56, 48]}, {"text": "卷纸款：15.3*6*24cm", "bbox": [447, 601, 279, 33], "confidence": 0.9948927164077759, "precise_height": 28, "text_color": [58, 50, 42]}, {"text": "湿巾款：15.3*12.6*11cm", "bbox": [448, 645, 301, 35], "confidence": 0.9942288994789124, "precise_height": 28, "text_color": [55, 45, 35]}, {"text": "产品材质", "bbox": [121, 695, 97, 29], "confidence": 0.9995378255844116, "precise_height": 20, "text_color": [72, 62, 52]}, {"text": "颜色", "bbox": [442, 692, 59, 34], "confidence": 0.9997738003730774, "precise_height": 20, "text_color": [84, 72, 61]}, {"text": "不锈钢+ABS", "bbox": [124, 737, 174, 32], "confidence": 0.996364414691925, "precise_height": 28, "text_color": [58, 50, 41]}, {"text": "胡桃木色", "bbox": [450, 737, 120, 33], "confidence": 0.9971750378608704, "precise_height": 28, "text_color": [62, 54, 46]}]}, {"image_path": "gemini_images/3.jpg", "image_name": "3.jpg", "text_regions_count": 5, "total_groups": 3, "single_text_groups": 2, "multi_text_groups": 1, "max_group_size": 3, "style_groups": [{"group_id": 1, "texts": ["店铺重要通知"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 40, "color": [178, 17, 20]}}, {"group_id": 2, "texts": ["本店铺所有产品价格均为出厂价格，不含开票税运价格，", "产品详情及价格仅供参考，各位如需大量采购或者定制", "请联系客服重新加税点报价，谢谢谅解"], "size": 3, "height_variance": 0.0, "color_variance": 0.6666666666666666, "representative_style": {"height": 34, "color": [7, 7, 6]}}, {"group_id": 3, "texts": ["确定收到"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 34, "color": [21, 23, 61]}}], "text_regions": [{"text": "店铺重要通知", "bbox": [383, 34, 227, 43], "confidence": 0.9935370087623596, "precise_height": 40, "text_color": [178, 17, 20]}, {"text": "本店铺所有产品价格均为出厂价格，不含开票税运价格，", "bbox": [133, 135, 884, 35], "confidence": 0.9942433834075928, "precise_height": 34, "text_color": [7, 7, 6]}, {"text": "产品详情及价格仅供参考，各位如需大量采购或者定制", "bbox": [134, 198, 889, 36], "confidence": 0.9976825714111328, "precise_height": 34, "text_color": [8, 8, 7]}, {"text": "请联系客服重新加税点报价，谢谢谅解", "bbox": [133, 260, 630, 35], "confidence": 0.9970007538795471, "precise_height": 34, "text_color": [6, 6, 5]}, {"text": "确定收到", "bbox": [469, 348, 140, 40], "confidence": 0.9608727693557739, "precise_height": 34, "text_color": [21, 23, 61]}]}, {"image_path": "gemini_images/4.png", "image_name": "4.png", "text_regions_count": 8, "total_groups": 4, "single_text_groups": 1, "multi_text_groups": 3, "max_group_size": 3, "style_groups": [{"group_id": 1, "texts": ["加粗钢管", "约15mm"], "size": 2, "height_variance": 0.0, "color_variance": 4.0, "representative_style": {"height": 32, "color": [9, 9, 9]}}, {"group_id": 2, "texts": ["加粗升级款", "常规薄款"], "size": 2, "height_variance": 0.0, "color_variance": 10.083333333333334, "representative_style": {"height": 36, "color": [235, 247, 247]}}, {"group_id": 3, "texts": ["约15*15mm加粗管架", "约10*10mm薄壁管架", "稳稳承托桌面不易摇晃"], "size": 3, "height_variance": 0.22222222222222224, "color_variance": 14.222222222222221, "representative_style": {"height": 28, "color": [63, 63, 63]}}, {"group_id": 4, "texts": ["框架薄易摇晃"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 27, "color": [94, 94, 94]}}], "text_regions": [{"text": "加粗钢管", "bbox": [502, 332, 147, 40], "confidence": 0.9988788962364197, "precise_height": 32, "text_color": [9, 9, 9]}, {"text": "约15mm", "bbox": [489, 374, 173, 44], "confidence": 0.999655544757843, "precise_height": 32, "text_color": [5, 5, 5]}, {"text": "加粗升级款", "bbox": [124, 685, 162, 36], "confidence": 0.998541533946991, "precise_height": 36, "text_color": [235, 247, 247]}, {"text": "常规薄款", "bbox": [486, 701, 129, 36], "confidence": 0.9989193677902222, "precise_height": 36, "text_color": [241, 241, 240]}, {"text": "约15*15mm加粗管架", "bbox": [66, 937, 297, 31], "confidence": 0.9965000748634338, "precise_height": 28, "text_color": [63, 63, 63]}, {"text": "约10*10mm薄壁管架", "bbox": [430, 928, 257, 31], "confidence": 0.9952241778373718, "precise_height": 27, "text_color": [71, 71, 71]}, {"text": "框架薄易摇晃", "bbox": [475, 959, 168, 29], "confidence": 0.9964394569396973, "precise_height": 27, "text_color": [94, 94, 94]}, {"text": "稳稳承托桌面不易摇晃", "bbox": [60, 969, 287, 31], "confidence": 0.9988234639167786, "precise_height": 27, "text_color": [63, 63, 63]}]}, {"image_path": "gemini_images/5.jpg", "image_name": "5.jpg", "text_regions_count": 6, "total_groups": 4, "single_text_groups": 2, "multi_text_groups": 2, "max_group_size": 2, "style_groups": [{"group_id": 1, "texts": ["花样收纳", "想吸哪里吸哪里"], "size": 2, "height_variance": 0.0, "color_variance": 0.75, "representative_style": {"height": 36, "color": [74, 51, 16]}}, {"group_id": 2, "texts": ["电视遥控"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 24, "color": [192, 177, 157]}}, {"group_id": 3, "texts": ["马桶遥控", "空调遥控"], "size": 2, "height_variance": 0.0, "color_variance": 3.75, "representative_style": {"height": 24, "color": [206, 193, 174]}}, {"group_id": 4, "texts": ["窗帘遥控"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 24, "color": [222, 208, 188]}}], "text_regions": [{"text": "花样收纳", "bbox": [181, 0, 162, 45], "confidence": 0.9994673728942871, "precise_height": 36, "text_color": [74, 51, 16]}, {"text": "想吸哪里吸哪里", "bbox": [123, 47, 277, 38], "confidence": 0.9943361282348633, "precise_height": 36, "text_color": [75, 53, 18]}, {"text": "电视遥控", "bbox": [78, 419, 79, 24], "confidence": 0.9994382858276367, "precise_height": 24, "text_color": [192, 177, 157]}, {"text": "马桶遥控", "bbox": [369, 419, 78, 24], "confidence": 0.9986273050308228, "precise_height": 24, "text_color": [206, 193, 174]}, {"text": "窗帘遥控", "bbox": [78, 793, 78, 23], "confidence": 0.9990564584732056, "precise_height": 24, "text_color": [222, 208, 188]}, {"text": "空调遥控", "bbox": [369, 793, 78, 24], "confidence": 0.9989780783653259, "precise_height": 24, "text_color": [206, 190, 168]}]}, {"image_path": "gemini_images/6.jpg", "image_name": "6.jpg", "text_regions_count": 17, "total_groups": 17, "single_text_groups": 17, "multi_text_groups": 0, "max_group_size": 1, "style_groups": [{"group_id": 1, "texts": ["韩国进口品牌"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 22, "color": [8, 3, 2]}}, {"group_id": 2, "texts": ["控油祛痘"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 87, "color": [80, 60, 49]}}, {"group_id": 3, "texts": ["肌肤焕新"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 83, "color": [79, 59, 50]}}, {"group_id": 4, "texts": ["tondl水杨酸祛痘舒护棉片"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 30, "color": [82, 68, 60]}}, {"group_id": 5, "texts": ["专利"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 43, "color": [246, 245, 241]}}, {"group_id": 6, "texts": ["平衡水油"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 17, "color": [128, 97, 70]}}, {"group_id": 7, "texts": ["净润控油"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 30, "color": [85, 58, 45]}}, {"group_id": 8, "texts": ["舒缓"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 48, "color": [247, 246, 242]}}, {"group_id": 9, "texts": ["1%盒引"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 39, "color": [139, 31, 37]}}, {"group_id": 10, "texts": ["水杨酸"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 22, "color": [144, 53, 55]}}, {"group_id": 11, "texts": ["清洁毛孔"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 13, "color": [135, 110, 90]}}, {"group_id": 12, "texts": ["维稳安肤"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 13, "color": [197, 181, 165]}}, {"group_id": 13, "texts": ["控油"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 34, "color": [174, 125, 125]}}, {"group_id": 14, "texts": ["微肤"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 26, "color": [141, 30, 35]}}, {"group_id": 15, "texts": ["舒级"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 13, "color": [160, 118, 114]}}, {"group_id": 16, "texts": ["净肤控油"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 65, "color": [247, 247, 243]}}, {"group_id": 17, "texts": ["肌肤细嫩透"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 56, "color": [247, 246, 245]}}], "text_regions": [{"text": "韩国进口品牌", "bbox": [268, 51, 142, 28], "confidence": 0.9985074996948242, "precise_height": 22, "text_color": [8, 3, 2]}, {"text": "控油祛痘", "bbox": [48, 175, 362, 92], "confidence": 0.9922345876693726, "precise_height": 87, "text_color": [80, 60, 49]}, {"text": "肌肤焕新", "bbox": [47, 261, 363, 96], "confidence": 0.9948450326919556, "precise_height": 83, "text_color": [79, 59, 50]}, {"text": "tondl水杨酸祛痘舒护棉片", "bbox": [50, 371, 357, 32], "confidence": 0.9735339283943176, "precise_height": 30, "text_color": [82, 68, 60]}, {"text": "专利", "bbox": [275, 470, 80, 43], "confidence": 0.9968056678771973, "precise_height": 43, "text_color": [246, 245, 241]}, {"text": "平衡水油", "bbox": [84, 480, 77, 26], "confidence": 0.9990872144699097, "precise_height": 17, "text_color": [128, 97, 70]}, {"text": "净润控油", "bbox": [60, 505, 126, 37], "confidence": 0.9992075562477112, "precise_height": 30, "text_color": [85, 58, 45]}, {"text": "舒缓", "bbox": [273, 505, 81, 48], "confidence": 0.9978935718536377, "precise_height": 48, "text_color": [247, 246, 242]}, {"text": "1%盒引", "bbox": [458, 512, 69, 39], "confidence": 0.6602082252502441, "precise_height": 39, "text_color": [139, 31, 37]}, {"text": "水杨酸", "bbox": [527, 533, 52, 26], "confidence": 0.990562915802002, "precise_height": 22, "text_color": [144, 53, 55]}, {"text": "清洁毛孔", "bbox": [94, 546, 58, 19], "confidence": 0.9991692304611206, "precise_height": 13, "text_color": [135, 110, 90]}, {"text": "维稳安肤", "bbox": [283, 553, 63, 18], "confidence": 0.9990487694740295, "precise_height": 13, "text_color": [197, 181, 165]}, {"text": "控油", "bbox": [462, 554, 57, 34], "confidence": 0.945313036441803, "precise_height": 34, "text_color": [174, 125, 125]}, {"text": "微肤", "bbox": [511, 560, 68, 26], "confidence": 0.639803409576416, "precise_height": 26, "text_color": [141, 30, 35]}, {"text": "舒级", "bbox": [543, 579, 36, 16], "confidence": 0.7186400890350342, "precise_height": 13, "text_color": [160, 118, 114]}, {"text": "净肤控油", "bbox": [38, 703, 243, 65], "confidence": 0.9928091168403625, "precise_height": 65, "text_color": [247, 247, 243]}, {"text": "肌肤细嫩透", "bbox": [370, 709, 339, 56], "confidence": 0.9988477826118469, "precise_height": 56, "text_color": [247, 246, 245]}]}, {"image_path": "gemini_images/7.jpg", "image_name": "7.jpg", "text_regions_count": 6, "total_groups": 6, "single_text_groups": 6, "multi_text_groups": 0, "max_group_size": 1, "style_groups": [{"group_id": 1, "texts": ["源自核心产区茶园"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 69, "color": [6, 6, 6]}}, {"group_id": 2, "texts": ["每一颗高品质的茶都是我们的初心"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 40, "color": [11, 11, 11]}}, {"group_id": 3, "texts": ["选自高山野长茶树"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 57, "color": [27, 27, 27]}}, {"group_id": 4, "texts": ["成长于白茶黄金生长区，气候温和空气湿润，"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 31, "color": [25, 19, 15]}}, {"group_id": 5, "texts": ["日照适度，且多为柔和漫射光，经过整个冬天的养分积"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 31, "color": [36, 28, 22]}}, {"group_id": 6, "texts": ["累，内含物质更丰富，造就了茶叶的鲜爽清甜。"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 35, "color": [33, 19, 11]}}], "text_regions": [{"text": "源自核心产区茶园", "bbox": [185, 35, 616, 86], "confidence": 0.9982680082321167, "precise_height": 69, "text_color": [6, 6, 6]}, {"text": "每一颗高品质的茶都是我们的初心", "bbox": [151, 145, 688, 48], "confidence": 0.9570189118385315, "precise_height": 40, "text_color": [11, 11, 11]}, {"text": "选自高山野长茶树", "bbox": [253, 378, 479, 57], "confidence": 0.9972829818725586, "precise_height": 57, "text_color": [27, 27, 27]}, {"text": "成长于白茶黄金生长区，气候温和空气湿润，", "bbox": [161, 592, 643, 31], "confidence": 0.9905527234077454, "precise_height": 31, "text_color": [25, 19, 15]}, {"text": "日照适度，且多为柔和漫射光，经过整个冬天的养分积", "bbox": [96, 644, 797, 31], "confidence": 0.9936602711677551, "precise_height": 31, "text_color": [36, 28, 22]}, {"text": "累，内含物质更丰富，造就了茶叶的鲜爽清甜。", "bbox": [143, 693, 680, 35], "confidence": 0.9961568713188171, "precise_height": 35, "text_color": [33, 19, 11]}]}, {"image_path": "gemini_images/8.jpg", "image_name": "8.jpg", "text_regions_count": 3, "total_groups": 1, "single_text_groups": 0, "multi_text_groups": 1, "max_group_size": 3, "style_groups": [{"group_id": 1, "texts": ["洗脸盆", "洗衣盆", "婴儿盆"], "size": 3, "height_variance": 0.0, "color_variance": 7.629629629629631, "representative_style": {"height": 36, "color": [218, 202, 178]}}], "text_regions": [{"text": "洗脸盆", "bbox": [303, 34, 94, 37], "confidence": 0.9980762600898743, "precise_height": 36, "text_color": [218, 202, 178]}, {"text": "洗衣盆", "bbox": [482, 396, 94, 36], "confidence": 0.9964215159416199, "precise_height": 36, "text_color": [221, 205, 183]}, {"text": "婴儿盆", "bbox": [181, 431, 95, 37], "confidence": 0.9967396855354309, "precise_height": 36, "text_color": [224, 208, 186]}]}, {"image_path": "gemini_images/9.png", "image_name": "9.png", "text_regions_count": 8, "total_groups": 3, "single_text_groups": 1, "multi_text_groups": 2, "max_group_size": 5, "style_groups": [{"group_id": 1, "texts": ["我们用心，用户才放心"], "size": 1, "height_variance": 0.0, "color_variance": 0.0, "representative_style": {"height": 48, "color": [227, 214, 214]}}, {"group_id": 2, "texts": ["小口袋", "大口袋"], "size": 2, "height_variance": 0.0, "color_variance": 2.25, "representative_style": {"height": 48, "color": [145, 121, 129]}}, {"group_id": 3, "texts": ["可放置水杯、饮料", "或一些小物品", "可以放一些零食", "饮料等也可以做", "垃圾桶哦"], "size": 5, "height_variance": 0.0, "color_variance": 4.666666666666667, "representative_style": {"height": 32, "color": [193, 175, 177]}}], "text_regions": [{"text": "我们用心，用户才放心", "bbox": [312, 50, 438, 48], "confidence": 0.996251106262207, "precise_height": 48, "text_color": [227, 214, 214]}, {"text": "小口袋", "bbox": [729, 346, 165, 59], "confidence": 0.9998927116394043, "precise_height": 48, "text_color": [145, 121, 129]}, {"text": "可放置水杯、饮料", "bbox": [674, 432, 284, 39], "confidence": 0.9982307553291321, "precise_height": 32, "text_color": [193, 175, 177]}, {"text": "或一些小物品", "bbox": [710, 477, 211, 35], "confidence": 0.9971723556518555, "precise_height": 32, "text_color": [190, 174, 175]}, {"text": "大口袋", "bbox": [162, 842, 170, 61], "confidence": 0.999851405620575, "precise_height": 48, "text_color": [142, 118, 126]}, {"text": "可以放一些零食", "bbox": [125, 948, 250, 41], "confidence": 0.9980462789535522, "precise_height": 32, "text_color": [193, 174, 177]}, {"text": "饮料等也可以做", "bbox": [118, 999, 263, 42], "confidence": 0.9993726015090942, "precise_height": 32, "text_color": [189, 167, 173]}, {"text": "垃圾桶哦", "bbox": [174, 1049, 150, 43], "confidence": 0.974453330039978, "precise_height": 32, "text_color": [191, 170, 175]}]}], "grouping_statistics": {"total_groups": 49, "single_text_groups": 34, "multi_text_groups": 15, "max_group_size": 5, "average_group_size": 1.489795918367347}, "statistics": {"average_groups_per_image": 4.9, "average_text_regions": 7.3, "min_text_regions": 3, "max_text_regions": 17, "single_text_group_ratio": 0.6938775510204082, "group_size_distribution": {"min": 1, "max": 5, "average": 1.489795918367347}}}