"""
第3次迭代优化的布局识别算法
生成时间: 2025-07-15T21:42:14.918951
"""

import math
import numpy as np
from sklearn.cluster import DBSCAN

def are_styles_similar_optimized(item1, item2, height_abs_thresh, height_rel_thresh, color_thresh):
    """
    判断两个文本项的样式是否相似（优化版）。
    
    Args:
        item1 (dict): 第一个文本项, e.g., {'precise_height': 20, 'text_color': (0,0,0)}
        item2 (dict): 第二个文本项
        height_abs_thresh (int): 高度绝对差值阈值
        height_rel_thresh (float): 高度相对差值阈值
        color_thresh (float): 颜色欧氏距离阈值
        
    Returns:
        bool: 如果样式相似则返回True，否则返回False。
    """
    h1 = item1['precise_height']
    h2 = item2['precise_height']
    c1 = item1['text_color']
    c2 = item2['text_color']

    # 1. 检查颜色相似性 (欧氏距离)
    # 颜色差异是视觉上非常重要的区分点，优先判断。
    color_dist = math.sqrt(sum([(a - b) ** 2 for a, b in zip(c1, c2)]))
    if color_dist > color_thresh:
        return False

    # 2. 检查高度相似性 (同时考虑绝对和相对差异)
    # 只要满足其中一个条件，就认为高度相似。
    height_diff = abs(h1 - h2)
    # 避免除以零错误
    if max(h1, h2) == 0:
        is_height_similar = height_diff <= height_abs_thresh
    else:
        is_height_similar = (height_diff <= height_abs_thresh) or \
                            (height_diff / max(h1, h2) <= height_rel_thresh)

    return is_height_similar

def group_text_styles_optimized(text_items, height_abs_thresh=5, height_rel_thresh=0.15, color_thresh=60):
    """
    使用DBSCAN和自定义距离矩阵进行优化的文字样式分组算法。
    优化点:
    - height_abs_thresh (5): 从4提升到5，增加对微小尺寸差异的容忍度，解决临界情况。
    - height_rel_thresh (0.15): 从0.20微调至0.15，与放宽的绝对阈值互补。
    - color_thresh (60): 从35大幅提升到60，以更好地处理光照、渐变、灰阶和压缩导致的颜色漂移。

    Args:
        text_items (list of dict): 包含所有文本区域数据的列表。
            每个dict应包含 'text' (str), 'precise_height' (int), 'text_color' (tuple) 等键。
        height_abs_thresh (int): 高度绝对差值阈值。
        height_rel_thresh (float): 高度相对差值阈值。
        color_thresh (float): 颜色欧氏距离阈值。

    Returns:
        list of list: 分组后的结果，每个子列表是一个样式组。
    """
    if not text_items or len(text_items) < 2:
        return [[item] for item in text_items]

    num_items = len(text_items)
    
    # 1. 预计算距离矩阵
    # 如果样式相似，距离为0.5；否则为1.0。
    # DBSCAN的eps设为0.7，可以确保只有相似的项被聚类。
    distance_matrix = np.full((num_items, num_items), 1.0)

    for i in range(num_items):
        distance_matrix[i, i] = 0.0 # 自身距离为0
        for j in range(i + 1, num_items):
            # 使用优化后的相似度判断函数
            if are_styles_similar_optimized(text_items[i], text_items[j], height_abs_thresh, height_rel_thresh, color_thresh):
                distance_matrix[i, j] = 0.5
                distance_matrix[j, i] = 0.5

    # 2. 应用DBSCAN
    db = DBSCAN(eps=0.7, min_samples=1, metric='precomputed')
    db.fit(distance_matrix)
    
    # 3. 根据DBSCAN的标签重构分组
    labels = db.labels_
    groups = {}
    for i, label in enumerate(labels):
        if label not in groups:
            groups[label] = []
        groups[label].append(text_items[i])
        
    return list(groups.values())
