"""
第5次迭代优化的布局识别算法
生成时间: 2025-07-15T21:50:58.375693
"""

import math
import numpy as np
from sklearn.cluster import DBSCAN

def are_styles_similar_v5(item1, item2, height_abs_thresh_strict, height_abs_thresh_loose, height_rel_thresh, color_thresh):
    """
    判断两个文本项的样式是否相似（版本5）。
    此版本使用经过优化的、更宽松的阈值来匹配视觉一致性。

    Args:
        item1 (dict): 第一个文本项, e.g., {'precise_height': 20, 'text_color': (0,0,0)}
        item2 (dict): 第二个文本项
        height_abs_thresh_strict (int): 严格的高度绝对差值阈值，用于捕捉微小误差。
        height_abs_thresh_loose (int): 宽松的高度绝对差值阈值，需与相对阈值配合使用。
        height_rel_thresh (float): 高度相对差值阈值。
        color_thresh (float): 颜色欧氏距离阈值。

    Returns:
        bool: 如果样式相似则返回True，否则返回False。
    """
    h1 = item1['precise_height']
    h2 = item2['precise_height']
    c1 = item1['text_color']
    c2 = item2['text_color']

    # 1. 检查颜色相似性 (欧氏距离) - 使用更宽松的阈值
    color_dist = math.sqrt(sum([(a - b) ** 2 for a, b in zip(c1, c2)]))
    if color_dist > color_thresh:
        return False

    # 2. 检查高度相似性 (混合逻辑)
    height_diff = abs(h1 - h2)
    
    # 满足极小绝对差异，直接判定为相似 (处理OCR抖动)
    if height_diff <= height_abs_thresh_strict:
        return True
        
    # 否则，必须同时满足较大的绝对差异和相对差异阈值
    # 避免除以零错误
    if max(h1, h2) == 0:
        # 如果两个高度都是0，则认为它们相似
        is_height_similar = True
    else:
        is_height_similar = (height_diff <= height_abs_thresh_loose) and \
                            (height_diff / max(h1, h2) <= height_rel_thresh)

    return is_height_similar

def group_text_styles_v5(text_items, height_abs_thresh_strict=3, height_abs_thresh_loose=9, height_rel_thresh=0.15, color_thresh=60):
    """
    使用DBSCAN和版本5的优化距离矩阵进行文字样式分组。
    优化点:
    - color_thresh (60): 从40大幅提升到60，解决因光照和压缩导致的颜色漂移问题，修复Image 2, 4, 5。
    - height_abs_thresh_loose (9): 从6提升到9，放宽绝对高度容忍。
    - height_rel_thresh (0.15): 从0.12提升到0.15，放宽相对高度容忍。
    - 以上高度参数的调整共同修复了Image 6中的分组过细问题。

    Args:
        text_items (list of dict): 包含所有文本区域数据的列表。
        ... (其他参数见are_styles_similar_v5)

    Returns:
        list of list: 分组后的结果。
    """
    if not text_items:
        return []
    if len(text_items) < 2:
        return [[item] for item in text_items]

    num_items = len(text_items)
    
    # 1. 预计算距离矩阵 (0.5 for similar, 1.0 for dissimilar)
    distance_matrix = np.full((num_items, num_items), 1.0)

    for i in range(num_items):
        distance_matrix[i, i] = 0.0
        for j in range(i + 1, num_items):
            if are_styles_similar_v5(text_items[i], text_items[j], 
                                     height_abs_thresh_strict, 
                                     height_abs_thresh_loose, 
                                     height_rel_thresh, 
                                     color_thresh):
                distance_matrix[i, j] = 0.5
                distance_matrix[j, i] = 0.5

    # 2. 应用DBSCAN进行聚类
    # eps=0.7 意味着距离小于0.7的（即我们标记为0.5的相似对）可以被看作邻居
    db = DBSCAN(eps=0.7, min_samples=1, metric='precomputed')
    db.fit(distance_matrix)
    
    # 3. 根据DBSCAN的标签重构分组
    labels = db.labels_
    groups = {}
    for i, label in enumerate(labels):
        if label not in groups:
            groups[label] = []
        groups[label].append(text_items[i])
        
    return list(groups.values())
